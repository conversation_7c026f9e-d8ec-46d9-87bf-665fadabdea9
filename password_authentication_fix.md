# Password Authentication Fix

## 问题描述
在测试环境中发现，即使输入相同的密码，注册和登录时也会认证失败。

## 问题根源
经过分析，发现了以下问题：

### 1. JOIN查询中密码字段丢失
在 `GetUserByUsername` 方法中，使用了复杂的 JOIN 查询来获取用户信息，但是 XORM 在处理 JOIN 查询时可能不会正确地映射所有字段，特别是 `Password` 和 `Salt` 字段。

### 2. 角色ID硬编码问题
在 `AddUser` 方法中，代码硬编码使用了 `models_field.RoleNormalUserId`（值为2），而不是使用传入的 `userService.RoleId`。

## 解决方案

### 1. 创建专用的登录验证方法
创建了新的 `GetUserForLogin` 方法，确保密码和盐值字段被正确获取：

```go
// GetUserForLogin gets user information specifically for login authentication
// This method ensures password and salt fields are properly retrieved
func GetUserForLogin(username string) *UserInfo {
    // First get the basic user data including password and salt
    basicUser := new(Users)
    hasUser, err := getExecutor().Where("username = ?", username).Get(basicUser)
    if err != nil {
        zap.S().Error("get user for login error!! ", err.Error())
        return nil
    }
    if !hasUser {
        return nil
    }

    // Then get the complete user info with joins
    user := new(UserInfo)
    count, err := getExecutor().Table("users").Alias("u").
        Join("INNER", []string{"roles", "r"}, "r.id = u.role_id").
        Join("INNER", []string{"memberships", "c"}, "c.id = u.membership_id").
        Join("INNER", []string{"emails", "e"}, "e.id = u.email_id").
        Where("u.username = ?", username).Get(user)
    if err != nil {
        zap.S().Error("get user for login error!! ", err.Error())
        return nil
    }
    if !count {
        return nil
    }

    // Ensure password and salt are properly set from the basic user data
    user.Users.Password = basicUser.Password
    user.Users.Salt = basicUser.Salt

    return user
}
```

### 2. 更新登录方法
更新 `Login` 方法使用新的 `GetUserForLogin` 方法：

```go
func (userService *UserService) Login() (*LoginResponse, error) {
    // Get user by username for login authentication
    user := models.GetUserForLogin(userService.Username)
    if user == nil {
        return nil, nil // User not found
    }

    // Verify password using bcrypt
    if !bcrypt.CheckPassword(userService.Password, user.Users.Salt, user.Users.Password) {
        return nil, nil // Invalid credentials
    }
    // ... rest of the method
}
```

### 3. 修复角色ID问题
修复 `AddUser` 方法中的角色ID硬编码问题：

```go
// Use the provided RoleId, or default to normal user if not specified
roleId := userService.RoleId
if roleId == 0 {
    roleId = int(models_field.RoleNormalUserId) // Default to normal user
}

user := models.Users{
    RoleId:       roleId,  // 使用传入的角色ID而不是硬编码
    MembershipId: userService.MembershipId,
    // ... other fields
}
```

### 4. 确保角色ID字段被保存
在 `AddUser` 方法中添加 `RoleId` 字段到要保存的字段列表中：

```go
var f []string
f = append(f, models_field.UserRoleId,  // 添加这一行
    models_field.UserMembershipId,
    models_field.UserEmailId,
    // ... other fields
)
```

## 测试步骤

### 1. 注册新用户
```bash
curl -X POST "http://localhost:8080/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpassword123",
    "name": "Test User",
    "email": "<EMAIL>",
    "gender": "male",
    "country": "China",
    "membership": 1,
    "role": 2
  }'
```

### 2. 使用相同密码登录
```bash
curl -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpassword123"
  }'
```

### 3. 验证结果
- 注册应该成功返回 200 状态码
- 登录应该成功返回 200 状态码和 JWT token
- 用户应该被分配正确的角色ID

## 日志调试
bcrypt 包中已经添加了详细的日志记录，可以通过查看日志来调试密码哈希和验证过程：

```
[HashPassword] Start - Password Length: X, Salt: XXXXX
[CheckPassword] Start - Password Length: X, Salt: XXXXX, HashedPassword: XXXXX
[CheckPassword] Comparison result: true/false
```

## 注意事项
1. 确保数据库中的用户表有正确的角色ID
2. 确保 bcrypt 的日志级别设置为 INFO 或 DEBUG 以查看详细信息
3. 如果仍有问题，检查数据库连接和事务处理
