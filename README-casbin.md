# Casbin Authorization Integration

This project has integrated Casbin for permission management, implementing Role-Based Access Control (RBAC).

## Dependencies

The following dependencies need to be added:

```bash
go get github.com/casbin/casbin/v2
go get github.com/casbin/xorm-adapter/v2
```

## Permission Model

The Casbin permission model used in this project is as follows:

```
[request_definition]
r = sub, obj, act

[policy_definition]
p = sub, obj, act

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = g(r.sub, p.sub) && (p.obj == "*" || r.obj == p.obj) && (p.act == "*" || r.act == p.act)
```

Where:
- `sub`: Subject, typically a user ID or role name
- `obj`: Resource, such as "user", "admin", etc.
- `act`: Action, such as "read", "write", "update", "delete", etc.

## Role Definitions

The system defines three default roles:

1. `admin`: Administrator role with all permissions
2. `user`: Regular user role with basic permissions
3. `guest`: Guest role with read-only permissions

## API Endpoints

The system provides the following API endpoints for permission management:

### Policy Management

- `POST /api/policies/initialize`: Initialize basic policies
- `POST /api/policies`: Add new policy rules
- `DELETE /api/policies`: Delete policy rules

### Role Management

- `POST /api/policies/roles`: Assign roles to users
- `DELETE /api/policies/roles`: Remove roles from users
- `GET /api/policies/roles/:id`: Get all roles for a user

## Usage

### In Routes

```go
// Using object-action permission verification
router.GET("/some-path", casbin.Authorize("resource", "read"), handler.SomeHandler)

// Using role verification
router.GET("/admin-path", casbin.AuthorizeByRole("admin"), handler.AdminHandler)
```

### Verifying Permissions in Code

```go
userService := service.UserService{
    Id: userId,
}

// Check if user has permission
if userService.CheckUserPermission("resource", "read") {
    // Has permission, perform operation
} else {
    // No permission, deny access
}

// Get user roles
roles := userService.GetUserRoles()
```

## Database Table

Casbin uses the `casbin_policies` table to store policy data with the following structure:

```
Id    int
PType string
V0    string
V1    string
V2    string
V3    string
V4    string
V5    string
```

Where:
- `PType`: Policy type, "p" represents policy rules, "g" represents role assignments
- `V0`, `V1`, `V2`: For "p" type, they represent sub, obj, act; for "g" type, V0 represents user, V1 represents role

## Initialization

The system automatically initializes Casbin at startup. If you need to manually initialize basic policies, you can call the `/api/policies/initialize` endpoint.
