# Admin Edit User API Test

## API Endpoint
```
PATCH /api/admin/users/:id
```

## Request Headers
```
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json
```

## Request Body Example
Based on your parameters:
```json
{
  "country": "China",
  "email": "<EMAIL>",
  "gender": "male",
  "membership": null,
  "name": "<PERSON>",
  "role": 1,
  "username": "<PERSON>"
}
```

## Example cURL Request
```bash
curl -X PATCH "http://localhost:8080/api/admin/users/123" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "country": "China",
    "email": "<EMAIL>",
    "gender": "male",
    "membership": null,
    "name": "<PERSON>",
    "role": 1,
    "username": "<PERSON>"
  }'
```

## Expected Response
### Success (200 OK)
```json
{
  "code": 200,
  "message": "User updated successfully",
  "data": null
}
```

### Error Responses
#### Invalid Parameters (400)
```json
{
  "code": 400,
  "message": "Invalid parameters"
}
```

#### User Not Found (500)
```json
{
  "code": 500,
  "message": "Failed to update user"
}
```

#### Email Already Exists (500)
```json
{
  "code": 500,
  "message": "Failed to update user"
}
```

## Field Validation Rules
- `country`: Required, max 16 characters
- `email`: Required, valid email format, max 64 characters
- `gender`: Required, must be "male" or "female"
- `membership`: Optional (nullable), integer ID
- `name`: Required, max 16 characters
- `role`: Required, integer (1 = admin, other = user)
- `username`: Required, 3-32 characters

## Notes
1. The user ID is passed as a URL parameter (:id)
2. If email is changed, a new verification email will be sent
3. Role changes will update Casbin permissions automatically
4. All updates are performed in a database transaction for consistency
