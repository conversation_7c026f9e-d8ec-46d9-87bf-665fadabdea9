# Stage 1: Build the application
FROM golang:alpine AS builder

# Set the working directory inside the container for the build stage
WORKDIR /app

# Copy go.mod and go.sum to download dependencies first.
# This leverages <PERSON><PERSON>'s layer caching.
# These files are expected to be at the root of the build context.
COPY go.mod go.sum ./
RUN go env -w GOPROXY=https://goproxy.io,direct && go mod download

# Copy the rest of the application's source code from the build context.
COPY . .

# Compile the application, creating a statically linked binary.
RUN CGO_ENABLED=0 GOOS=linux go build -o ./api-server ./cmd/api-server

# Stage 2: Create the final, lightweight image
FROM alpine:latest

# Set the working directory for the final image
WORKDIR /app

# Copy the compiled binary from the 'builder' stage
COPY --from=builder /app/api-server .

# Copy the configuration files from the 'config' directory
# located at the root of the build context.
COPY config ./config

# Expose the port the application runs on, as defined in config/app.yaml
EXPOSE 3001

# Set the entrypoint for the container to run the application
ENTRYPOINT ["./api-server"]
