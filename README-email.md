# Email Service Documentation

This document describes the email service implementation for the Next Meeting Backend application.

## Overview

The email service provides functionality for sending various types of emails to users, including:

- Welcome emails for new users
- Password reset emails
- Meeting invitation emails
- Meeting reminder emails

## Configuration

Email settings are configured in the `conf/app.yaml` file:

```yaml
email:
  host: "smtp.example.com"
  port: 587
  username: "<EMAIL>"
  password: "your-password"
  from: "Next Meeting <<EMAIL>>"
  template_path: "internal/templates"
```

### Configuration Parameters

- `host`: SMTP server hostname
- `port`: SMTP server port
- `username`: SMTP authentication username
- `password`: SMTP authentication password
- `from`: Email sender address (with optional display name)
- `template_path`: Path to the email templates directory

## Email Templates

HTML email templates are located in the `internal/templates/email/` directory:

- `welcome.html`: Sent to new users upon registration
- `reset_password.html`: Sent when a user requests a password reset
- `meeting_invitation.html`: Sent when a user is invited to a meeting
- `meeting_reminder.html`: Sent as a reminder for upcoming meetings

## Usage

### Service Initialization

The email service is initialized during application startup in `cmd/api-server/main.go`:

```go
emailConfig := email.Config{
    Host:     app.EmailConfig.Host,
    Port:     app.EmailConfig.Port,
    Username: app.EmailConfig.Username,
    Password: app.EmailConfig.Password,
    From:     app.EmailConfig.From,
}
emailService = email.NewService(emailConfig, app.EmailConfig.TemplatePath)
```

### Accessing the Email Service

To use the email service in your code:

```go
import "next-meeting-backend/internal/pkg/email"

// Get the email service instance
emailSvc := email.GetEmailService()

// Send a welcome email
err := emailSvc.SendWelcomeEmail("<EMAIL>", "username")
if err != nil {
    // Handle error
}
```

### Available Email Methods

- `SendWelcomeEmail(to string, username string) error`
- `SendPasswordResetEmail(to string, username string, resetLink string) error`
- `SendMeetingInvitation(to string, username string, meetingTitle string, meetingTime string, meetingLink string) error`
- `SendMeetingReminder(to string, username string, meetingTitle string, meetingTime string, meetingLink string) error`

## Password Reset Flow

The password reset functionality uses the email service to send reset links to users:

1. User requests a password reset via `/api/auth/forgot-password` endpoint
2. System generates a reset token and sends an email with a reset link
3. User clicks the link and submits a new password via `/api/auth/reset-password` endpoint
4. System verifies the token and updates the user's password

## Integration with Casbin Authorization

The email service respects the existing Casbin authorization system. The password reset functionality is available to all users without authentication, but other email-related features (like sending meeting invitations) should be protected by appropriate Casbin policies.

## Dependencies

The email functionality relies on the following external package:

- `gopkg.in/gomail.v2`: For composing and sending emails

To install this dependency:

```bash
go get gopkg.in/gomail.v2
```
