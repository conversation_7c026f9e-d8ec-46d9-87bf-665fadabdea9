package jwt

import (
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/pkg/jwt"
	"next-meeting-backend/internal/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// JWT middleware for authentication
func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		var token string

		// Try to get token from different sources
		// 1. From Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) == 2 && parts[0] == "Bearer" {
				token = parts[1]
			}
		}

		// 2. From query parameter
		if token == "" {
			token = c.Query("token")
		}

		// 3. From cookie
		if token == "" {
			token, _ = c.<PERSON>ie("token")
		}

		// Check if token exists
		if token == "" {
			utils.RespondWithError(c, consts.InvalidParams, "Missing authentication token")
			c.Abort()
			return
		}

		// Parse and validate token
		claims, err := jwt.ParseToken(token)
		if err != nil {
			utils.RespondWithError(c, consts.ErrorAuthCheckTokenFail)
			c.Abort()
			return
		}

		// Check if token is expired
		if time.Now().Unix() > claims.ExpiresAt.Unix() {
			utils.RespondWithError(c, consts.ErrorAuthCheckTokenTimeout)
			c.Abort()
			return
		}

		// Set user information in context for later use
		c.Set("userId", claims.Id) // Set userId from claims.Id
		// Optionally, if username is also needed elsewhere in the context:
		// c.Set("username", claims.Username)
		c.Next()
	}
}

// RequireEmailVerification middleware to check if email is verified
func RequireEmailVerification() gin.HandlerFunc {
	return func(c *gin.Context) {
		emailVerified, exists := c.Get("email_verified")
		if !exists {
			utils.RespondWithError(c, consts.ErrorAuthCheckTokenFail, "Authentication required")
			c.Abort()
			return
		}

		if verified, ok := emailVerified.(bool); !ok || !verified {
			utils.RespondWithError(c, consts.ErrorAuthCheckTokenFail, "Email not verified, please verify your email first")
			c.Abort()
			return
		}

		c.Next()
	}
}
