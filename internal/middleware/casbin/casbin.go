package casbin

import (
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Authorize is a middleware for Casbin authorization
func Authorize(obj string, act string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from the context (set by JWT middleware)
		userId, exists := c.Get("userId")
		if !exists {
			zap.S().Error("User ID not found in context")
			utils.RespondWithError(c, consts.ErrorAuthCheckTokenFail, "Unauthorized: user not authenticated")
			c.Abort()
			return
		}

		// Convert user ID to string for Casbin
		sub := strconv.Itoa(userId.(int))

		// Check if the user has permission
		if !models.CheckPermission(sub, obj, act) {
			zap.S().Warnf("User %s has no permission to %s on %s", sub, act, obj)
			utils.RespondWithError(c, consts.ErrorAuthCheckPermissionDenied, "Forbidden: insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}

// AuthorizeByRole is a middleware that checks if the user has a specific role
func AuthorizeByRole(role string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from the context (set by JWT middleware)
		userId, exists := c.Get("userId")
		if !exists {
			zap.S().Error("User ID not found in context")
			utils.RespondWithError(c, consts.ErrorAuthCheckTokenFail, "Unauthorized: user not authenticated")
			c.Abort()
			return
		}

		// Convert user ID to string for Casbin
		sub := strconv.Itoa(userId.(int))

		// Get roles for the user
		roles := models.GetRolesForUser(sub)

		// Check if the user has the required role
		hasRole := false
		for _, r := range roles {
			if r == role {
				hasRole = true
				break
			}
		}

		if !hasRole {
			zap.S().Warnf("User %s does not have the required role: %s", sub, role)
			utils.RespondWithError(c, consts.ErrorAuthCheckPermissionDenied, "Forbidden: insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}
