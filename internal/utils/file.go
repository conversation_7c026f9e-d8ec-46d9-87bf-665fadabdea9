package utils

import (
	"bytes"
	"errors"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"mime/multipart"
	"next-meeting-backend/internal/pkg/consts"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
)

func ReadFileToByte(f multipart.File) (b []byte, n int, e error) {
	n, e = f.Read(b)
	if e != nil {
		return nil, -1, e
	}

	return b, n, nil
}

// CacheFile 缓存文件到本地（可选）
func CacheFile(objectName string, dir consts.CacheDir, data []byte) error {
	localPath := filepath.Join(string(dir), strings.ReplaceAll(objectName, "/", "_"))
	return os.WriteFile(localPath, data, 0644)
}

func ValidateFile(fileHeader *multipart.FileHeader, maxSize int64, allowedTypes map[string]bool) error {
	// 检查文件大小
	if fileHeader.Size > maxSize {
		return errors.New("file size limit exceeded, current size: " + fmt.Sprintf("%d bytes", fileHeader.Size))
	}

	// 检查文件类型
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	if !allowedTypes[ext] {
		return errors.New("file type not allowed")
	}

	return nil
}

func GenerateRemoteFilename(originalFilename string, userID int, dir consts.RemoteDir) string {
	ext := strings.ToLower(filepath.Ext(originalFilename))
	timestamp := time.Now().Format("20060102150405")
	sanitizedName := strings.ReplaceAll(originalFilename, filepath.Ext(originalFilename), "")
	sanitizedName = strings.ReplaceAll(sanitizedName, " ", "_")

	// 移除特殊字符
	specialChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	for _, char := range specialChars {
		sanitizedName = strings.ReplaceAll(sanitizedName, char, "")
	}

	// 限制文件名长度
	if len(sanitizedName) > 30 {
		sanitizedName = sanitizedName[:30]
	}

	return fmt.Sprintf("%s%s_user_%d_%s%s", dir, sanitizedName, userID, timestamp, ext)
}

func CompressFile(fileBytes []byte, ext string) (compressedBytes []byte, contentType consts.ContentType, err error) {
	// 根据文件类型处理
	switch ext {
	case ".jpg", ".jpeg":
		contentType = consts.HttpContentTypeJpeg
		compressedBytes, err = CompressImage(fileBytes, consts.HttpContentTypeJpeg)
	case ".png":
		contentType = consts.HttpContentTypePng
		compressedBytes, err = CompressImage(fileBytes, consts.HttpContentTypePng)
	case ".pdf":
		contentType = consts.HttpContentTypePdf
		compressedBytes, err = CompressPDF(fileBytes)
	case ".doc", ".docx":
		contentType = consts.HttpContentTypeWord
		compressedBytes = fileBytes

	default:
		return []byte(""), "", errors.New("file type not supported")
	}
	return compressedBytes, contentType, nil
}

// CompressImage 压缩图片
func CompressImage(fileBytes []byte, contentType consts.ContentType) ([]byte, error) {
	// 解码图片
	img, _, err := image.Decode(bytes.NewReader(fileBytes))
	if err != nil {
		return nil, errors.New("image is not compressed")
	}

	// 创建缓冲区存储压缩后的图片
	buf := new(bytes.Buffer)

	// 根据图片类型进行压缩
	switch contentType {
	case consts.HttpContentTypeJpeg, consts.HttpContentTypeJpg:
		err = jpeg.Encode(buf, img, &jpeg.Options{Quality: consts.ImageQuality})
	case consts.HttpContentTypePng:
		encoder := png.Encoder{
			CompressionLevel: png.BestCompression,
		}
		err = encoder.Encode(buf, img)
	default:
		return fileBytes, nil // 如果不是支持的图片类型，返回原始数据
	}

	if err != nil {
		return nil, errors.New("image is not compressed")
	}

	return buf.Bytes(), nil
}

// CompressPDF 压缩PDF文件
func CompressPDF(fileBytes []byte) ([]byte, error) {

	// 创建输入和输出缓冲区
	inBuf := bytes.NewReader(fileBytes)
	outBuf := new(bytes.Buffer)

	// 执行PDF压缩
	err := api.Optimize(inBuf, outBuf, &model.Configuration{})
	if err != nil {
		return nil, fmt.Errorf("压缩PDF失败: %w", err)
	}

	return outBuf.Bytes(), nil
}
