package utils

import (
	"go.uber.org/zap"
	"next-meeting-backend/internal/app/models_field"
)

type RolesUtil struct {
	RoleId   models_field.RoleNumber
	RoleName models_field.Role
}

var roleMap = map[models_field.RoleNumber]models_field.Role{
	models_field.RoleAdminId:      models_field.RoleAdmin,
	models_field.RoleNormalUserId: models_field.RoleNormalUser,
}

func (r *RolesUtil) GetRoleName(roleId models_field.RoleNumber) models_field.Role {
	return roleMap[roleId]
}

func (r *RolesUtil) GetRoleNameByInt(roleId int) string {
	id := r.GetDefaultOrRoleId(roleId)
	return string(roleMap[id])
}

func (r *RolesUtil) GetRoleStringName(roleId models_field.RoleNumber) string {
	return string(roleMap[roleId])
}

func (r *RolesUtil) GetRoleId(roleName models_field.Role) models_field.RoleNumber {
	for id, name := range roleMap {
		if name == roleName {
			return id
		}
	}
	zap.S().Errorf("Role %v not found", roleName)
	return 0
}

func (r *RolesUtil) GetRoleIntId(roleName models_field.Role) int {
	for id, name := range roleMap {
		if name == roleName {
			return int(id)
		}
	}
	zap.S().Error("Role not found")
	return 0
}

func (r *RolesUtil) GetDefaultOrRoleId(roleId int) models_field.RoleNumber {
	if roleId == 0 {
		return models_field.RoleNormalUserId
	}

	return models_field.RoleNumber(roleId)
}

func (r *RolesUtil) GetDefaultOrRoleIntId(roleId int) int {
	if roleId == 0 {
		return int(models_field.RoleNormalUserId)
	}

	return roleId
}
