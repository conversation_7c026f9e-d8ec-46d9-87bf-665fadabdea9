package service

import (
	"errors"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/models_field"
	"next-meeting-backend/internal/pkg/email"
	"next-meeting-backend/internal/utils"

	"go.uber.org/zap"
)

type AdminUserService struct {
	models.Users
	models.Emails
}

func (s *AdminUserService) GetAllUsers(filter string, limit, offset int) ([]models.UserInfo, error) {
	um := models.UserModel{}
	m := make(map[string]interface{})
	if filter != "" {
		m["name LIKE ?"] = "%" + filter + "%"
		m["email LIKE ?"] = "%" + filter + "%"
		m["username LIKE ?"] = "%" + filter + "%"
	}
	users, err := um.FilterUsers(m, limit, offset)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (s *AdminUserService) EditUser() error {
	esc := email.GetEmailService()
	um := models.UserModel{}
	em := models.EmailModel{}
	var token, link string
	// First check if user exists
	user, err := um.GetUserById(s.Users.Id)
	if err != nil {
		return err
	}
	if user.Users.Id == 0 {
		return errors.New("user not found")
	}

	var cols []string
	// Start a transaction for atomic updates
	sess := models.NewTransaction()
	defer sess.CloseSession()

	sess.BeginTransaction()
	// Handle email update if email has changed
	if s.Email != user.Emails.Email {
		// Check if the new email already exists
		existingEmail, err := em.CheckEmailExist(&s.Emails)
		if err != nil {
			sess.RollbackTransaction()
			return err
		}
		if existingEmail {
			sess.RollbackTransaction()
			return errors.New("email already exists")
		}
		token, link = email.BuildVerifyEmailLink()

		// Create new email record
		emailRecord := models.Emails{
			Email:         s.Email,
			EmailVerified: false,
		}
		ts := tokenService{}
		if err = ts.AddTokenToDb(s.Users.Id, token); err != nil {
			sess.RollbackTransaction()
			return err
		}

		// Insert the new email record
		emailId, err := em.AddEmail(&emailRecord)
		if err != nil {
			zap.S().Errorf("Failed to add new email record: %v", err)
			sess.RollbackTransaction()
			return err
		}

		// Update user's email_id to point to the new email record
		user.Users.EmailId = emailId
		cols = append(cols, models_field.UserEmailId)
	}

	// Handle role changes with Casbin
	if s.RoleId != 0 && s.RoleId != user.RoleId {
		policyService := PolicyService{
			UserId: s.Users.Id,
		}

		// Get current roles
		currentRoles := policyService.GetUserRoles()

		// Remove all current roles
		for _, role := range currentRoles {
			policyService.RemoveRoleFromUser(role)
		}
		util := utils.RolesUtil{}
		roleId := util.GetDefaultOrRoleIntId(s.RoleId)
		roleName := util.GetRoleNameByInt(roleId)

		if _, err = policyService.AssignRoleToUser(roleName); err != nil {
			sess.RollbackTransaction()
			zap.S().Errorf("Failed to assign new role %s to user %d", roleName, s.Users.Id)
			return err
		}
		cols = append(cols, models_field.UserRoleId)
	}

	if s.Name != user.Name {
		cols = append(cols, models_field.UserName)
	}
	if s.MembershipId != 0 && s.MembershipId != user.MembershipId {
		cols = append(cols, models_field.UserMembershipId)
	}
	if s.Gender != user.Gender {
		cols = append(cols, models_field.UserGender)
	}
	if s.Country != user.Country {
		cols = append(cols, models_field.UserCountry)
	}

	if len(cols) == 0 {
		sess.RollbackTransaction()
		return nil
	}
	err = um.UpdateUser(&s.Users, cols...)
	if err != nil {
		sess.RollbackTransaction()
		return err
	}

	if token != "" {
		err = esc.SendVerificationEmail(s.Email, s.Username, link)
		if err != nil {
			sess.RollbackTransaction()
			return err
		}
	}

	// Commit the transaction
	sess.CommitTransaction()
	zap.S().Infof("User %s updated successfully", s.Username)
	return nil
}
