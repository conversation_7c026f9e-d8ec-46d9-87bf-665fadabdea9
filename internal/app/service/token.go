package service

import (
	"go.uber.org/zap"
	"next-meeting-backend/internal/app/models"
	"time"
)

type tokenService struct {
}

func (s *tokenService) AddTokenToDb(userId int, token string) error {
	t := models.Tokens{
		UserId:    userId,
		Token:     token,
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}
	tm := models.TokensModel{}
	err := tm.AddToken(&t)
	if err != nil {
		zap.S().Error(err.Error())
		return err
	}

	return nil
}
