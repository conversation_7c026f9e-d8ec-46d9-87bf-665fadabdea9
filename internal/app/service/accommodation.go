package service

import (
	"errors"
	"next-meeting-backend/internal/app/models"

	"go.uber.org/zap"
)

const (
	ExecutorSystem = "System"
)

type AccommodationService struct {
	models.Hotels
	models.Rooms
	models.RoomReservations

	PartnerUserId int
}

func (s *AccommodationService) GetHotelRooms() ([]*models.RoomInfo, error) {
	am := models.AccommodationModel{}

	// 获取所有的酒店房间
	var roomInfo []*models.RoomInfo
	err := am.GetAllRooms(&roomInfo)
	if err != nil {
		zap.S().Error("Get Rooms failed: ", err.Error())
		return nil, errors.New("get Rooms failed")
	}

	for _, room := range roomInfo {
		room.Total, err = s.CalculateRemainingRoom(room.Rooms.Id, room.Total, room.Obligate)
		if err != nil {
			return nil, err
		}
	}
	return roomInfo, nil
}

func (s *AccommodationService) AddAccommodations() error {
	am := models.AccommodationModel{}
	um := models.UserModel{}
	// 不可重复预定
	am.UserId = s.UserId
	v := models.RoomReservations{UserId: s.UserId}
	exists, err := am.ExistsReservation(&v)
	if err != nil {
		zap.S().Error("Get Room Reservations failed: ", err.Error())
		return err
	}
	if exists {
		zap.S().Warn("User already has a reservation")
		return errors.New("user already has a reservation")
	}

	// 计算剩余房间
	var room models.Rooms
	room.Id = s.RoomReservations.RoomId
	err = am.GetRoom(&room)
	remaining, err := s.CalculateRemainingRoom(s.RoomReservations.RoomId, room.Total, room.Obligate)
	if err != nil {
		return err
	}
	if remaining <= 0 {
		return errors.New("no room available")
	}

	reversal := models.RoomReservations{
		RoomId:       s.RoomReservations.RoomId,
		SharedOption: s.RoomReservations.SharedOption,
		UserId:       s.RoomReservations.UserId,
		CheckinDate:  s.RoomReservations.CheckinDate,
		CheckoutDate: s.RoomReservations.CheckoutDate,
	}

	sess := models.NewTransaction()
	defer sess.CloseSession()
	sess.BeginTransaction()

	// SharedOption字段2表示和其他人同住，所以需要有另一个人的标识信息，通过去数据库查询得到id来新增到数据表中
	if s.PartnerUserId > 0 && s.SharedOption == 2 {
		// 判断id是否存在于数据库中
		u, err := um.GetUser(&models.Users{Id: s.PartnerUserId}, "id")
		if err != nil || u == nil {
			zap.S().Error("Add Room Reservation failed: Partner User not found")
			return errors.New("partner user not found")
		}

		ar := models.AssignedRooms{
			ExecutorUsername: ExecutorSystem,
		}
		arId, err := am.AddRoomAssignment(ar)
		if err != nil || arId == 0 {
			return errors.New("add room assignment failed")
		}

		reversal.UserId = u.Id
		reversal.IsAssigned = true
		reversal.AssignedRoomId = arId
		// 添加同住人
		success, err := am.AddRoomReservationInfo(&reversal,
			"room_id",
			"shared_option",
			"assigned_room_id",
			"user_id",
			"checkin_date",
			"checkout_date",
			"is_assigned",
		)
		if err != nil {
			zap.S().Error("Add Room Reservation failed: ", err.Error())
			sess.RollbackTransaction()
			return err
		}
		if success {
			zap.S().Info("Add Room Reservation success")
		}
		// SharedOption字段1表示不同住，可以直接分配房间
	} else if s.SharedOption == 1 {
		reversal.IsAssigned = true

		ar := models.AssignedRooms{
			ExecutorUsername: ExecutorSystem,
		}
		arId, err := am.AddRoomAssignment(ar)
		if err != nil || arId == 0 {
			sess.RollbackTransaction()
			return errors.New("add room assignment failed")
		}
		reversal.AssignedRoomId = arId
	}

	// 添加自己
	reversal.UserId = s.UserId
	success, err := am.AddRoomReservationInfo(&reversal,
		"room_id",
		"shared_option",
		"assigned_room_id",
		"user_id",
		"checkin_date",
		"checkout_date",
		"is_assigned",
	)
	if err != nil {
		zap.S().Error("Add Room Reservation failed: ", err.Error())
		sess.RollbackTransaction()
		return err
	}
	if success {
		zap.S().Info("Add Room Reservation success")
		sess.CommitTransaction()
	}

	return nil
}

func (s *AccommodationService) GetMyReservation() (models.RoomReservationInfo, error) {
	am := models.AccommodationModel{}
	// 先根据user id找到记录
	roomReservationInfo := models.RoomReservationInfo{}
	roomReservationInfo.UserId = s.UserId
	err := am.GetRoomReservations(&roomReservationInfo)
	if err != nil {
		zap.S().Error("Get Room Reservations failed: ", err.Error())
		return models.RoomReservationInfo{}, err
	}

	// 先判断是否有合住人，没有合住人直接返回
	// 1是单独住，3是等待指派合住人
	if roomReservationInfo.RoomReservations.SharedOption == 1 {
		return roomReservationInfo, nil
	}
	if roomReservationInfo.RoomReservations.SharedOption == 3 && roomReservationInfo.RoomReservations.IsAssigned == false {
		return roomReservationInfo, nil
	}

	// 如果有合住人则再根据assigned_room_id找到另一个合住人
	roommateInfo, err := am.GetRoommateByAssignedRoomId(roomReservationInfo.RoomReservations.AssignedRoomId, s.UserId)
	if err != nil {
		return models.RoomReservationInfo{}, err
	}
	roomReservationInfo.Roommate.RoommateInfo = roommateInfo

	return roomReservationInfo, nil
}

func (s *AccommodationService) CalculateRemainingRoom(roomId, total, obligate int) (remaining int, err error) {
	am := models.AccommodationModel{}
	count, err := am.CounterAssignedRooms(roomId)
	if err != nil {
		zap.S().Error("Count Room Reservations failed: ", err.Error())
		return remaining, err
	}
	remaining = total - obligate - count
	return remaining, nil
}

func (s *AccommodationService) CancelMyReservation() error {
	return nil
}

func (s *AccommodationService) CheckUserReservation(username string) (int, error) {
	// 根据username查询对应id
	um := models.UserModel{}
	uid := um.GetUserIdByUsername(username)
	if uid == 0 {
		zap.S().Debug("Check User Reservation Failed, username: ", username)
		return 0, errors.New("user not found")
	}
	am := models.AccommodationModel{}
	exists, err := am.CheckUserReservation(uid)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, errors.New("user has reservation")
	}
	return uid, nil
}
