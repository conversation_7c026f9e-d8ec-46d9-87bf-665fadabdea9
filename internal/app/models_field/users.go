package models_field

// Users 表字段常量定义
const (
	// 表名
	UsersTable = "users"

	// 主键
	UserId = "id"

	// 外键
	UserRoleId       = "role_id"
	UserMembershipId = "membership_id"
	UserEmailId      = "email_id"

	// 用户认证信息
	UserUsername = "username"
	UserPassword = "password"
	UserSalt     = "salt"

	// 用户基本信息
	UserAvatarUrl    = "avatar_url"
	UserName         = "name"
	UserGender       = "gender"
	UserPhone        = "phone"
	UserCountry      = "country"
	UserOrganization = "organization"

	// 用户扩展信息
	UserPosition   = "position"
	UserBio        = "bio"
	UserInterest   = "interest"
	UserExpertises = "expertises"
	UserPaid       = "paid"

	// 时间信息
	UserCreateTime = "create_time"
)

type Gender string

// 性别枚举值
const (
	GenderMale   Gender = "male"
	GenderFemale Gender = "female"
)
