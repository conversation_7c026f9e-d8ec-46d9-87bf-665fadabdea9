package models_field

// Transactions 表字段常量定义
const (
	// TransactionsTable 表名
	TransactionsTable = "transactions"

	// TransactionId 主键
	TransactionId = "id"

	// TransactionRequestUser 申请人
	TransactionRequestUser = "request_user"

	// TransactionTeamUsers 同组的用户id, 因为xorm不可以存储数组, 所以采用varchar存储以逗号分隔的用户id
	TransactionTeamUsers = "team_users"

	TransactionMethodId = "method_id"

	// TransactionReviewId 审核信息
	TransactionReviewId = "review_id"

	// TransactionRegion 区分转账到是国内账户还是国外账户
	TransactionRegion = "region"

	// TransactionTransactionId 转账凭证
	TransactionTransactionId = "transaction_id"

	// TransactionMethod 转账方法
	TransactionMethod = "method"

	// TransactionAmount 总转账金额
	TransactionAmount = "amount"

	// TransactionEvidence 转账凭证, 图片地址
	TransactionEvidence = "evidence"

	// TransactionNotes 备注
	TransactionNotes = "notes"

	// TransactionPaymentTime 转账时间
	TransactionPaymentTime = "transaction_time"
)

// Reviews 表字段常量定义
const (
	// ReviewsTable 表名
	ReviewsTable = "reviews"

	// ReviewId 主键
	ReviewId = "id"

	// ReviewReviewer 审核人
	ReviewReviewer = "reviewer"

	// ReviewApproved 审核结果
	ReviewApproved = "approved"

	// ReviewReviewTime 审核时间
	ReviewReviewTime = "review_time"
)

// Invoices 表字段常量定义
const (
	// InvoicesTable 表名
	InvoicesTable Table = "invoices"

	// InvoiceId 主键
	InvoiceId Columns = "id"

	InvoiceTransactionId Columns = "tid"

	// InvoiceOwner 发票拥有者
	InvoiceOwner Columns = "owner"

	// InvoiceTaxId 税号
	InvoiceTaxId Columns = "tax_id"

	// InvoiceOrganization 组织名称
	InvoiceOrganization Columns = "organization"

	// InvoiceAddress 地址
	InvoiceAddress Columns = "address"

	// InvoiceContactName 联系人姓名
	InvoiceContactName Columns = "contact_name"

	// InvoiceContactEmail 联系人邮箱
	InvoiceContactEmail Columns = "contact_email"

	// InvoiceContactPhone 联系人电话
	InvoiceContactPhone Columns = "contact_phone"

	// InvoiceInstruction 发票说明
	InvoiceInstruction Columns = "instruction"

	// InvoiceStatus 发票状态
	InvoiceStatus Columns = "status"

	// InvoiceCreateTime 发票创建时间
	InvoiceCreateTime Columns = "create_time"
)
