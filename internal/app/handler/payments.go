package handler

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.uber.org/zap"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"
	"strings"
	"time"
)

type PaymentHandler struct {
}

type PaymentRequest struct {
	Region          string  `form:"region" binding:"required,oneof=domestic international"`
	Method          int     `form:"method" binding:"required"`
	TransactionID   string  `form:"transaction_id" binding:"required"`
	TransactionTime string  `form:"transaction_time" binding:"required"`
	Amount          float32 `form:"amount" binding:"required"`
	TeamMembers     string  `form:"team_members"`
	Notes           string  `form:"notes"`
}

type AddInvoiceRequest struct {
	Tid          int    `form:"tid" json:"tid"`
	TaxId        string `form:"tax_id" json:"taxId" binding:"required"`
	Organization string `form:"organization" json:"organization" binding:"required"`
	Address      string `form:"address" json:"address" binding:"required"`
	ContactName  string `form:"contact_name" json:"contact_name" binding:"required"`
	ContactEmail string `form:"contact_email" json:"contact_email" binding:"required"`
	ContactPhone string `form:"contact_phone" json:"contact_phone" binding:"required"`
	Instruction  string `form:"instruction" json:"instruction" binding:"required"`
}

func (h *PaymentHandler) CreatePayment(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown user")
		return
	}
	var paymentData PaymentRequest
	if err = c.ShouldBindWith(&paymentData, binding.Form); err != nil {
		zap.S().Errorf("Failed to bind payment data: %v", err)
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	file, fileHeader, err := c.Request.FormFile("receipt")
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "no receipt file")
		return
	}

	if fileHeader.Size > int64(consts.ReceiptMaxSize) {
		utils.RespondWithError(c, consts.ErrorUploadFileSizeLimitExceeded)
		return
	}

	t, err := time.ParseInLocation(time.DateTime, paymentData.TransactionTime, time.Local)
	now := time.Now()
	zap.S().Infof("parsed time: %v, now: %v", t, now)
	if err != nil { // 如果时间转换失败或者订单发生时间比现在晚，都会验证不过
		utils.RespondWithError(c, consts.InvalidParams, "invalid payment date")
		return
	}

	// 前端发来的数据项可能会包含空格，要删除掉每个逗号间的空格
	var teamMembers []string
	if paymentData.TeamMembers != "" {
		temp := strings.Split(paymentData.TeamMembers, ",")
		for _, item := range temp {
			teamMembers = append(teamMembers, strings.TrimSpace(item))
		}
	}
	paymentService := service.PaymentService{
		Users: models.Users{
			Id: userId,
		},
		Transactions: models.Transactions{
			RequestUser:     userId,
			TeamUsers:       strings.Join(teamMembers, ","),
			Region:          paymentData.Region,
			TransactionId:   paymentData.TransactionID,
			MethodId:        paymentData.Method,
			Amount:          paymentData.Amount,
			Notes:           paymentData.Notes,
			TransactionTime: t,
		},
	}

	err = paymentService.AddPayment(file, fileHeader)
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to add payment")
		return
	}
	utils.RespondWithSuccess(c, nil, "added successfully")
}

func (h *PaymentHandler) GetPayment(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown user")
		return
	}
	svc := service.PaymentService{
		Users: models.Users{
			Id: userId,
		},
	}
	transactions, err := svc.GetPayment()
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to get payment")
		return
	}
	utils.RespondWithSuccess(c, transactions, "get payment successfully")

}

func (h *PaymentHandler) UpdatePayment(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown id")
		return
	}
	var paymentData PaymentRequest
	if err = c.ShouldBindWith(&paymentData, binding.Form); err != nil {
		zap.S().Errorf("Failed to bind payment data: %v", err)
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	t, err := time.ParseInLocation(time.DateTime, paymentData.TransactionTime, time.Local)
	if err != nil { // 如果时间转换失败或者订单发生时间比现在晚，都会验证不过
		utils.RespondWithError(c, consts.InvalidParams, "invalid payment date")
		return
	}

	svc := service.PaymentService{
		Transactions: models.Transactions{
			Id:              id,
			TeamUsers:       paymentData.TeamMembers,
			Region:          paymentData.Region,
			TransactionId:   paymentData.TransactionID,
			MethodId:        paymentData.Method,
			Amount:          paymentData.Amount,
			Notes:           paymentData.Notes,
			TransactionTime: t,
		},
	}

	// file是可空的，空文件代表使用旧的文件链接。
	file, fileHeader, err := c.Request.FormFile("receipt")
	if err == nil {
		if fileHeader.Size > int64(consts.ReceiptMaxSize) {
			utils.RespondWithError(c, consts.ErrorUploadFileSizeLimitExceeded)
			return
		}
	}

	err = svc.UpdatePayment(file, fileHeader)
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to update payment")
		return
	}
	utils.RespondWithSuccess(c, nil, "updated successfully")
}

func (h *PaymentHandler) AddInvoiceInfo(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown user")
		return
	}
	var invoiceData AddInvoiceRequest
	err = c.ShouldBindWith(&invoiceData, binding.Form)
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, err.Error())
		return
	}

	svc := service.PaymentService{
		Invoices: models.Invoices{
			Owner:        userId,
			Tid:          invoiceData.Tid,
			TaxId:        invoiceData.TaxId,
			Organization: invoiceData.Organization,
			Address:      invoiceData.Address,
			ContactName:  invoiceData.ContactName,
			ContactEmail: invoiceData.ContactEmail,
			ContactPhone: invoiceData.ContactPhone,
			Instruction:  invoiceData.Instruction,
			CreateTime:   time.Now(),
		},
	}
	err = svc.AddInvoiceInfo()
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to add invoice")
		return
	}
	utils.RespondWithSuccess(c, nil, "added successfully")
}

func (h *PaymentHandler) GetInvoiceInfo(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown user")
		return
	}
	svc := service.PaymentService{
		Users: models.Users{
			Id: userId,
		},
	}
	invoices, err := svc.GetInvoiceInfo()
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to get invoice")
		return
	}
	utils.RespondWithSuccess(c, invoices, "get invoice successfully")
}

func (h *PaymentHandler) UpdateInvoiceInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown id")
		return
	}
	var invoiceData AddInvoiceRequest
	err = c.ShouldBindWith(&invoiceData, binding.Form)
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, err.Error())
		return
	}
	svc := service.PaymentService{
		Invoices: models.Invoices{
			Tid:          id,
			TaxId:        invoiceData.TaxId,
			Organization: invoiceData.Organization,
			Address:      invoiceData.Address,
			ContactName:  invoiceData.ContactName,
			ContactEmail: invoiceData.ContactEmail,
			ContactPhone: invoiceData.ContactPhone,
			Instruction:  invoiceData.Instruction,
		},
	}
	err = svc.UpdateInvoiceInfo()
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to update invoice")
		return
	}
	utils.RespondWithSuccess(c, nil, "updated successfully")
}
