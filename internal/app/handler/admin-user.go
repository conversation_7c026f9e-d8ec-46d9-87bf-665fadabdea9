package handler

import (
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type AdminUserHandler struct {
}

type AdminEditUserData struct {
	Country    string `json:"country" binding:"required,max=16"`
	Email      string `json:"email" binding:"required,email,max=64"`
	Gender     string `json:"gender" binding:"required,oneof=male female"`
	Membership int    `json:"membership" binding:"required,oneof=1 2"`
	Name       string `json:"name" binding:"required,max=16"`
	Role       int    `json:"role" binding:"required"`
	Username   string `json:"username" binding:"required,min=3,max=32"`
}

type AdminUpdateUserData struct {
	Organization string   `json:"organization" binding:"max=128"`
	Biography    string   `json:"bio" binding:"max=2048"`
	Expertise    []string `json:"expertises"`
	Position     string   `json:"position"  binding:"max=32"`
	Interest     string   `json:"interest"  binding:"max=128"`
}

type AdminAddUserData struct {
	RoleId       int    `json:"role_id" binding:"required"`
	MembershipId int    `json:"membership_id" binding:"required"`
	Username     string `json:"username" binding:"required,min=3,max=32"`
	Password     string `json:"password" binding:"required,min=8,max=32"`
	Name         string `json:"name" binding:"required,max=16"`
}

type AdminGetUsersData struct {
	RoleId       int    `json:"role_id" binding:"required"`
	MembershipId int    `json:"membership_id" binding:"required"`
	Username     string `json:"username" binding:"required,min=3,max=32"`
	Password     string `json:"password" binding:"required,min=8"`
	Name         string `json:"name" binding:"required,max=16"`
	Limit        int    `json:"limit"`
	Offset       int    `json:"offset"`
}

type AdminGetUsersRequest struct {
	Filter string `form:"filter"`
	Limit  int    `form:"limit"`
	Offset int    `form:"offset"`
}

func (h *AdminUserHandler) AdminGetUsersHandler(c *gin.Context) {
	var data AdminGetUsersRequest
	if err := c.ShouldBindWith(&data, binding.Form); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	s := service.AdminUserService{}
	users, err := s.GetAllUsers(data.Filter, data.Limit, data.Offset)
	if err != nil {
		utils.RespondWithError(c, consts.Error)
		return
	}
	utils.RespondWithSuccess(c, users, "get users successfully")

}

func (h *AdminUserHandler) AdminUpdateUserHandler(c *gin.Context) {
	// Get user ID from URL parameter
	userId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid user ID")
		return
	}

	// Bind request data
	var userInfo AdminEditUserData
	if err = c.ShouldBindBodyWithJSON(&userInfo); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	// Create user service with the data
	userService := service.AdminUserService{
		Users: models.Users{
			Id:           userId,
			RoleId:       userInfo.Role,
			MembershipId: userInfo.Membership,
			Username:     userInfo.Username,
			Name:         userInfo.Name,
			Gender:       userInfo.Gender,
			Country:      userInfo.Country,
		},
		Emails: models.Emails{
			Email: userInfo.Email,
		},
	}
	// Call EditUser method
	err = userService.EditUser()
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to update user")
		return
	}

	utils.RespondWithSuccess(c, nil, "User updated successfully")
}

func (h *AdminUserHandler) AdminDeleteUserHandler(c *gin.Context) {

}

func (h *AdminUserHandler) AdminAddUserHandler(c *gin.Context) {
	var userInfo RegisterData
	if err := c.ShouldBindBodyWithJSON(&userInfo); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	userService := service.UserService{
		Users: models.Users{
			RoleId:       userInfo.RoleId,
			MembershipId: userInfo.MembershipId,
			Username:     userInfo.Username,
			Password:     userInfo.Password,
			Name:         userInfo.Name,
			Gender:       userInfo.Gender,
			Country:      userInfo.Country,
			CreateTime:   time.Now(),
		},
		Emails: models.Emails{
			Email: userInfo.Email,
		},
	}
	err := userService.RegisterUser()
	if err != nil {
		utils.RespondWithError(c, consts.Error)
		return
	}
	utils.RespondWithSuccess(c, nil, "User added successfully")

}
