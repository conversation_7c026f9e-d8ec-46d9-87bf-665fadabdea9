package models

import (
	"time"

	"go.uber.org/zap"
)

type AccommodationModel struct {
	Hotels
	Rooms
	RoomReservations
}

type Hotels struct {
	Id                  int       `json:"hotel_id" xorm:"not null pk autoincr"`
	Name                string    `json:"name"`
	ChineseName         string    `json:"chinese_name"`
	Location            string    `json:"location"`
	ContactName         string    `json:"contact_name"`
	ContactPhone        string    `json:"contact_phone"`
	ImageUrl            string    `json:"image_url"`
	Lon                 float64   `json:"lon"`
	Lat                 float64   `json:"lat"`
	DefaultCheckinDate  time.Time `json:"default_checkin_date"`
	DefaultCheckoutDate time.Time `json:"default_checkout_date"`
}

type Rooms struct {
	Id           int `json:"room_id" xorm:"not null pk autoincr"`
	HotelId      int
	Type         string    `json:"type" xorm:"type"`
	Price        float32   `json:"price" xorm:"price"`
	Total        int       `json:"total" xorm:"total"`
	Obligate     int       `json:"obligate" xorm:"obligate"`
	CheckinDate  time.Time `json:"checkin_date" xorm:"checkin_date"`
	CheckoutDate time.Time `json:"checkout_date" xorm:"checkout_date"`
}

type RoomReservations struct {
	Id             int
	RoomId         int
	SharedOption   int
	AssignedRoomId int
	UserId         int       `json:"occupant" xorm:"user_id"`
	CheckinDate    time.Time `json:"checkin_date"`
	CheckoutDate   time.Time `json:"checkout_date"`
	IsAssigned     bool      `json:"is_assigned" xorm:"is_assigned"`
	CreatedTime    time.Time `json:"created_time" xorm:"created"`
}

type AssignedRooms struct {
	Id               int       `json:"id" xorm:"not null pk autoincr"`
	ExecutorUsername string    `json:"executor_username" xorm:"executor_username"`
	CreatedTime      time.Time `json:"created_time" xorm:"created"`
}

type SharedOptions struct {
	Id   int
	Name string `json:"shared_option"`
}

type RoomTypes struct {
	Id   int
	Name string `json:"room_type"`
}

type RoomInfo struct {
	Rooms     `xorm:"extends"`
	Hotels    `xorm:"extends"`
	RoomTypes `xorm:"extends"`
}

type RoomReservationInfo struct {
	AssignedRooms    `xorm:"extends"`
	Hotels           `xorm:"extends"`
	Rooms            `xorm:"extends"`
	RoomReservations `xorm:"extends"`
	RoomTypes        `xorm:"extends"`
	SharedOptions    `xorm:"extends"`
	UserBaseInfo     `xorm:"extends"`
	Roommate         `json:"roommate"`
}

type Roommate struct {
	RoommateInfo `xorm:"extends"`
}

type RoommateInfo struct {
	AssignedRooms    `xorm:"extends"`
	RoomReservations `xorm:"extends"`
	UserBaseInfo     `xorm:"extends"`
}

var ReservationInfoCols = []string{
	"u.name,", "u.username", "u.organization", "u.gender", "u.country",
	"h.name", "h.location", "r.type", "r.price", "rr.checkin_date", "rr.checkout_date",
}

func (m *AccommodationModel) GetHotels(v *Hotels, cols ...string) (err error) {
	b, err := buildOrmCols(getExecutor(), cols...).Get(v)
	if err != nil {
		return err
	}
	if !b {
		return nil
	}
	return nil
}

func (m *AccommodationModel) GetRoom(v *Rooms, cols ...string) error {
	_, err := buildOrmCols(getExecutor(), cols...).Get(v)
	if err != nil {
		return err
	}
	return nil

}

func (m *AccommodationModel) GetRooms(v *RoomInfo, cols ...string) (err error) {
	_, err = buildOrmCols(getExecutor(), cols...).Table("rooms").Alias("r").
		Join("INNER", []string{"hotels", "h"}, "h.id = r.hotel_id").
		Join("INNER", []string{"room_types", "rt"}, "rt.id = r.type").Get(&v)
	return err
}

func (m *AccommodationModel) GetAllRooms(v *[]*RoomInfo, cols ...string) (err error) {
	return buildOrmCols(getExecutor(), cols...).Table("rooms").Alias("r").
		Join("INNER", []string{"hotels", "h"}, "h.id = r.hotel_id").
		Join("INNER", []string{"room_types", "rt"}, "rt.id = r.type").Find(v)
}

func (m *AccommodationModel) GetRoommateByAssignedRoomId(roomAssignId, userId int, cols ...string) (roommateInfo RoommateInfo, err error) {
	_, err = buildOrmCols(getExecutor(), cols...).Table("assigned_rooms").Alias("ar").
		Join("INNER", []string{"room_reservations", "rr"}, "ar.id = rr.assigned_room_id").
		Join("INNER", []string{"users", "u"}, "rr.user_id = u.id").
		Where("rr.user_id != ?", userId).And("ar.id = ?", roomAssignId).Get(&roommateInfo)
	if err != nil {
		return roommateInfo, err
	}
	return roommateInfo, nil
}

func (m *AccommodationModel) ExistsReservation(v *RoomReservations) (b bool, err error) {
	return buildOrmCols(getExecutor()).Exist(v)
}

func (m *AccommodationModel) GetRoomReservations(v *RoomReservationInfo, cols ...string) (err error) {
	b, err := buildOrmCols(getExecutor(), cols...).
		Table("room_reservations").Alias("rr").
		Join("INNER", []string{"rooms", "r"}, "rr.room_id = r.id").
		Join("INNER", []string{"hotels", "h"}, "r.hotel_id = h.id").
		Join("INNER", []string{"room_types", "rt"}, "r.type = rt.id").
		Join("INNER", []string{"shared_options", "so"}, "rr.shared_option = so.id").
		Join("INNER", []string{"users", "u"}, "rr.user_id = u.id").
		Join("LEFT", []string{"assigned_rooms", "ar"}, "rr.assigned_room_id = ar.id").Get(v)
	if err != nil {
		return err
	}
	if !b {
		return nil
	}
	return nil

}

func (m *AccommodationModel) CounterAssignedRooms(roomId int) (counter int, err error) {
	sql := "SELECT count(distinct ar.id) as remaining FROM assigned_rooms AS ar LEFT JOIN room_reservations AS rr ON ar.id = rr.assigned_room_id WHERE (is_assigned IS true) AND (room_id = ?)"
	var arr []map[string]interface{}
	arr, err = getExecutor().SQL(sql, roomId).QueryInterface()
	if err != nil {
		return 0, err
	}
	counter = int(arr[0]["remaining"].(int32))
	return counter, err
}

// AddRoomReservationInfo 房间预定
func (m *AccommodationModel) AddRoomReservationInfo(info *RoomReservations, cols ...string) (success bool, err error) {
	sess := buildOrmCols(getExecutor(), cols...)

	res, err := sess.Insert(info)
	if err != nil {
		zap.S().Error("Add Reservation Info failed", err.Error())
		return false, err
	}
	if res <= 0 {
		zap.S().Warn("Add Reservation Info reported success but no rows affected or ID not set.")
		return false, nil
	}
	zap.S().Debug("Reservation Info inserted successfully into DB: ", res)

	return true, err
}

func (m *AccommodationModel) AddRoomAssignment(info AssignedRooms, cols ...string) (id int, err error) {
	sess := buildOrmCols(getExecutor(), cols...)

	res, err := sess.Insert(&info)
	if err != nil {
		zap.S().Error("Add Assignment Info failed", err.Error())
		return 0, err
	}
	zap.S().Debug("Assignment Info inserted successfully into DB: ", res)

	return info.Id, nil
}

func (m *AccommodationModel) CheckUserReservation(userId int) (exists bool, err error) {
	return buildOrmCols(getExecutor()).Where("user_id = ?", userId).Exist(&RoomReservations{})
}

//func (m *AccommodationModel) CancelRoomReservation(userId int) (success bool, err error) {
//	_, err = getExecutor().Where("user_id = ?", userId).Delete(RoomReservations{})
//	if err != nil {
//		return false, err
//	}
//	return true, nil
//}
