package models

import (
	"go.uber.org/zap"
	"time"
)

type TokensModel struct {
}

// Tokens 用来保存重置密码或验证邮箱时的token
type Tokens struct {
	Id        int    `json:"tid" xorm:"not null pk autoincr"`
	UserId    int    `json:"user_id"`
	Token     string `json:"token"`
	ExpiresAt time.Time
}

func (t *TokensModel) AddToken(token *Tokens, cols ...string) error {
	sess := buildOrmCols(getExecutor(), cols...)
	_, err := sess.Insert(token)
	if err != nil {
		zap.S().Error(err.Error())
		return err
	}
	return nil
}

func (t *TokensModel) RemoveToken(token string) error {
	_, err := getExecutor().Where("token = ?", token).Delete(Tokens{})
	if err != nil {
		zap.S().Error(err.<PERSON><PERSON><PERSON>())
		return err
	}
	return nil
}

func (t *TokensModel) GetToken(tokens *Tokens, cols ...string) (*Tokens, error) {
	sess := buildOrmCols(getExecutor(), cols...)
	_, err := sess.Get(tokens)
	if err != nil {
		zap.S().Error(err.Error())
		return nil, err
	}
	return tokens, nil
}
