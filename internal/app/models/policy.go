package models

import (
	"errors"
	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/model"
	xormadapter "github.com/casbin/xorm-adapter/v2"
	"go.uber.org/zap"
)

type CasbinPolicies struct {
	Id    int
	PType string
	V0    string
	V1    string
	V2    string
	V3    string
	V4    string
	V5    string
}

var enforcer *casbin.Enforcer

// InitCasbin initializes the Casbin enforcer with the RBAC model and adapter
func InitCasbin() {
	// Initialize the adapter with the existing database connection
	adapter, err := xormadapter.NewAdapterByEngine(engine)
	if err != nil {
		zap.S().Panicf("Failed to create casbin adapter: %v", err)
		return // Should be unreachable due to panic
	}
	if adapter == nil {
		zap.S().Panic("Casbin adapter is nil after creation attempt without an explicit error.")
		return // Should be unreachable due to panic
	}

	// Define the RBAC model as a string
	rbacModel := `
[request_definition]
r = sub, obj, act

[policy_definition]
p = sub, obj, act

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = g(r.sub, p.sub) && (p.obj == "*" || r.obj == p.obj) && (p.act == "*" || r.act == p.act)
`

	// Load the model from string
	m, err := model.NewModelFromString(rbacModel)
	if err != nil {
		zap.S().Panicf("Failed to create casbin model from string: %v", err)
		return // Should be unreachable due to panic
	}

	// Create the enforcer
	e, err := casbin.NewEnforcer(m, adapter)
	if err != nil {
		zap.S().Panicf("Failed to create casbin enforcer: %v", err)
		return // Should be unreachable due to panic
	}
	if e == nil {
		zap.S().Panic("Casbin enforcer is nil after creation attempt without an explicit error.")
		return // Should be unreachable due to panic
	}

	// Load the policy from DB
	err = e.LoadPolicy()
	if err != nil {
		// LoadPolicy failure might be normal on first run if the table is empty or doesn't exist yet (adapter might create it).
		// However, if it's due to other reasons (e.g., DB connection lost, permissions), it's an issue.
		zap.S().Warnf("Casbin failed to load policy from DB: %v. This might be normal on first run or if no policies exist. If issues persist, check DB connection and table 'casbin_rule'.", err)
		// Depending on strictness, you might choose to panic here too if LoadPolicy is critical for startup.
	}

	enforcer = e
	zap.S().Info("Casbin initialized successfully")
}

// GetEnforcer returns the Casbin enforcer instance
func GetEnforcer() *casbin.Enforcer {
	return enforcer
}

// CheckPermission checks if the user has permission to access the resource
func CheckPermission(sub string, obj string, act string) bool {
	if enforcer == nil {
		zap.S().Error("Casbin enforcer not initialized")
		return false
	}

	ok, err := enforcer.Enforce(sub, obj, act)
	if err != nil {
		zap.S().Error("Failed to check permission: ", err)
		return false
	}
	return ok
}

// AddPolicy adds a policy rule to Casbin
func AddPolicy(sub string, obj string, act string) bool {
	if enforcer == nil {
		zap.S().Error("Casbin enforcer not initialized")
		return false
	}

	ok, err := enforcer.AddPolicy(sub, obj, act)
	if err != nil {
		zap.S().Error("Failed to add policy: ", err)
		return false
	}

	// Save policy to database and reload to ensure consistency
	if ok {
		if err := enforcer.SavePolicy(); err != nil {
			zap.S().Errorf("Failed to save policy to database: %v", err)
		}
		if err := enforcer.LoadPolicy(); err != nil {
			zap.S().Errorf("Failed to reload policy from database: %v", err)
		}
		zap.S().Infof("Policy added and reloaded: %s, %s, %s", sub, obj, act)
	}
	return ok
}

// AddRoleForUser adds a role for a user
func AddRoleForUser(user string, role string) (ok bool, err error) {
	if enforcer == nil {
		err = errors.New("casbin enforcer not initialized")
		return false, err
	}

	ok, err = enforcer.AddGroupingPolicy(user, role)
	if err != nil {
		err = errors.New("Failed to add role for user: " + err.Error())
		return false, err
	}

	// Save policy to database and reload to ensure consistency
	if ok {
		if saveErr := enforcer.SavePolicy(); saveErr != nil {
			zap.S().Errorf("Failed to save policy to database: %v", saveErr)
		}
		if loadErr := enforcer.LoadPolicy(); loadErr != nil {
			zap.S().Errorf("Failed to reload policy from database: %v", loadErr)
		}
		zap.S().Infof("Role added and reloaded: user %s, role %s", user, role)
	}
	return ok, nil
}

// RemovePolicy removes a policy rule from Casbin
func RemovePolicy(sub string, obj string, act string) bool {
	if enforcer == nil {
		zap.S().Error("Casbin enforcer not initialized")
		return false
	}

	ok, err := enforcer.RemovePolicy(sub, obj, act)
	if err != nil {
		zap.S().Error("Failed to remove policy: ", err)
		return false
	}

	// Save policy to database and reload to ensure consistency
	if ok {
		if err := enforcer.SavePolicy(); err != nil {
			zap.S().Errorf("Failed to save policy to database: %v", err)
		}
		if err := enforcer.LoadPolicy(); err != nil {
			zap.S().Errorf("Failed to reload policy from database: %v", err)
		}
		zap.S().Infof("Policy removed and reloaded: %s, %s, %s", sub, obj, act)
	}
	return ok
}

// RemoveRoleForUser removes a role from a user
func RemoveRoleForUser(user string, role string) bool {
	if enforcer == nil {
		zap.S().Error("Casbin enforcer not initialized")
		return false
	}

	ok, err := enforcer.RemoveGroupingPolicy(user, role)
	if err != nil {
		zap.S().Error("Failed to remove role for user: ", err)
		return false
	}

	// Save policy to database and reload to ensure consistency
	if ok {
		if err := enforcer.SavePolicy(); err != nil {
			zap.S().Errorf("Failed to save policy to database: %v", err)
		}
		if err := enforcer.LoadPolicy(); err != nil {
			zap.S().Errorf("Failed to reload policy from database: %v", err)
		}
		zap.S().Infof("Role removed and reloaded: user %s, role %s", user, role)
	}
	return ok
}

// GetRolesForUser gets roles for a user
func GetRolesForUser(user string) []string {
	if enforcer == nil {
		zap.S().Error("Casbin enforcer not initialized")
		return nil
	}

	roles, err := enforcer.GetRolesForUser(user)
	if err != nil {
		zap.S().Error("Failed to get roles for user: ", err)
		return nil
	}
	return roles
}

// GetUsersForRole gets users for a role
func GetUsersForRole(role string) []string {
	if enforcer == nil {
		zap.S().Error("Casbin enforcer not initialized")
		return nil
	}

	users, err := enforcer.GetUsersForRole(role)
	if err != nil {
		zap.S().Error("Failed to get users for role: ", err)
		return nil
	}
	return users
}

// ReloadPolicy manually reloads the policy from database
func ReloadPolicy() error {
	if enforcer == nil {
		return errors.New("casbin enforcer not initialized")
	}

	err := enforcer.LoadPolicy()
	if err != nil {
		zap.S().Errorf("Failed to reload policy from database: %v", err)
		return err
	}

	zap.S().Info("Policy reloaded successfully from database")
	return nil
}

// SaveAndReloadPolicy saves current policy to database and reloads it
func SaveAndReloadPolicy() error {
	if enforcer == nil {
		return errors.New("casbin enforcer not initialized")
	}

	// Save current policy to database
	if err := enforcer.SavePolicy(); err != nil {
		zap.S().Errorf("Failed to save policy to database: %v", err)
		return err
	}

	// Reload policy from database
	if err := enforcer.LoadPolicy(); err != nil {
		zap.S().Errorf("Failed to reload policy from database: %v", err)
		return err
	}

	zap.S().Info("Policy saved and reloaded successfully")
	return nil
}

// GetAllPolicies returns all policy rules
func GetAllPolicies() [][]string {
	if enforcer == nil {
		zap.S().Error("Casbin enforcer not initialized")
		return nil
	}

	policy, err := enforcer.GetPolicy()
	if err != nil {
		return nil
	}
	return policy
}

// GetAllRoles returns all role assignments
func GetAllRoles() ([][]string, error) {
	if enforcer == nil {
		zap.S().Error("Casbin enforcer not initialized")
		return nil, nil
	}

	return enforcer.GetGroupingPolicy()
}

// ClearAllPolicies clears all policies and roles (use with caution)
func ClearAllPolicies() error {
	if enforcer == nil {
		return errors.New("casbin enforcer not initialized")
	}

	// Clear all policies
	if _, err := enforcer.RemoveFilteredPolicy(0); err != nil {
		zap.S().Errorf("Failed to clear policies: %v", err)
		return err
	}

	// Clear all roles
	if _, err := enforcer.RemoveFilteredGroupingPolicy(0); err != nil {
		zap.S().Errorf("Failed to clear roles: %v", err)
		return err
	}

	// Save and reload
	if err := SaveAndReloadPolicy(); err != nil {
		return err
	}

	zap.S().Info("All policies and roles cleared successfully")
	return nil
}
