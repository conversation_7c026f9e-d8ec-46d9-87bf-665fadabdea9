package models

import (
	"errors"
	"time"

	"go.uber.org/zap"
)

type UserModel struct {
	Users
	Roles
	Memberships
}

type Users struct {
	Id           int       `json:"uid" xorm:"not null pk autoincr"`
	RoleId       int       `json:"role_id"`
	MembershipId int       `json:"membership_id"`
	EmailId      int       `json:"email_id"`
	Username     string    `json:"username"`
	Password     string    `json:"-"`
	Salt         string    `json:"-"`
	AvatarUrl    string    `json:"avatar_url"`
	Name         string    `json:"name"`
	Gender       string    `json:"gender"`
	Country      string    `json:"country"`
	Organization string    `json:"organization"`
	Phone        string    `json:"phone"`
	Position     string    `json:"position"`
	Bio          string    `json:"bio"`
	Interest     string    `json:"interest"`
	Expertises   string    `json:"expertise"`
	Paid         bool      `json:"paid"`
	CreateTime   time.Time `json:"u_create_time"`
}

type UserBaseInfo struct {
	Username     string `json:"username"`
	Name         string `json:"name"`
	Gender       string `json:"gender"`
	Country      string `json:"country"`
	Organization string `json:"organization"`
}

type Roles struct {
	Id   int    `json:"role_id" xorm:"not null pk autoincr"`
	Role string `json:"role"`
}

type Memberships struct {
	Id         int    `json:"mid" xorm:"not null pk autoincr"`
	Membership string `json:"membership"`
	Price      float32
}

type UserInfo struct {
	Users       `xorm:"extends"`
	Roles       `xorm:"extends"`
	Memberships `xorm:"extends"`
	Emails      `xorm:"extends"`
}

func (userInfo *UserInfo) TableName() string {
	return "users"
}

func (model *UserModel) GetMemberships() *Memberships {
	var m Memberships
	err := getExecutor().Find(&m)
	if err != nil {
		zap.S().Debug("get memberships error!! ", err.Error())
		return nil
	}
	return &m
}

// AddUser adds a user and syncs role with Casbin
func (model *UserModel) AddUser(user *Users, cols ...string) bool {
	sess := buildOrmCols(getExecutor(), cols...)

	res, err := sess.Insert(user)
	if err != nil {
		zap.L().Error("AddUser failed", zap.Error(err))
		return false
	}
	if res <= 0 {
		zap.S().Warn("AddUser reported success but no rows affected or ID not set.")
		return false
	}
	zap.S().Debug("User inserted successfully into DB: ", user)

	return true
}

// UpdateUser updates user information and syncs role with Casbin
func (model *UserModel) UpdateUser(user *Users, cols ...string) error {
	sess := buildOrmCols(getExecutor(), cols...)
	res, err := sess.ID(user.Id).Update(user)
	if err != nil {
		zap.L().Error("UpdateUser failed", zap.Error(err))
		return err
	}
	if res <= 0 {
		zap.S().Warn("UpdateUser reported success but no rows affected.")
		return errors.New("no rows affected")
	}
	return nil
}

func (model *UserModel) UpdateUserPaidStatusById(id int, paid bool) error {
	if id == 0 {
		zap.S().Warnln("UpdateUserPaidStatusById invalid user id")
		return errors.New("invalid user id")
	}
	_, err := getExecutor().Cols("paid").Where("id = ?", id).Update(Users{Paid: paid})
	if err != nil {
		// Use %v for the usernames slice, or strings.Join for a comma-separated string
		zap.S().Errorf("UpdateUserPaidStatus: Failed to update paid status for id %v: %v", id, err)
		return err
	}
	return nil
}

func (model *UserModel) GetUser(users *Users, cols ...string) (res *Users, err error) {
	sess := buildOrmCols(getExecutor(), cols...)
	has, err := sess.Get(users)
	if err != nil {
		zap.S().Error("get user error!! ", err.Error())
		return nil, err
	}
	if !has {
		return nil, nil
	}

	return users, nil
}

func (model *UserModel) GetUserInfo(res *UserInfo, cols ...string) (*UserInfo, error) {
	sess := buildOrmCols(getExecutor(), cols...)
	has, err := sess.Table("users").Alias("u").
		Join("INNER", []string{"roles", "r"}, "r.id = u.role_id").
		Join("INNER", []string{"memberships", "c"}, "c.id = u.membership_id").
		Join("INNER", []string{"emails", "e"}, "e.id = u.email_id").
		Get(res)
	if err != nil {
		zap.S().Error("get user error!! ", err.Error())
		return nil, err
	}
	if !has {
		return nil, nil
	}

	return res, nil
}

func (model *UserModel) CounterUsers() (count int64, err error) {
	count, err = getExecutor().Count(new(Users))
	if err != nil {
		zap.S().Error("count users error!! ", err.Error())
		return 0, err
	}
	return count, nil
}

// FilterUsers gets all user information
func (model *UserModel) FilterUsers(filter map[string]interface{}, limit, offset int) (users []UserInfo, err error) {
	users = make([]UserInfo, 0)
	idx := 0

	sess := getExecutor().Table("users").Alias("u").
		Join("INNER", []string{"roles", "r"}, "r.id = u.role_id").
		Join("INNER", []string{"memberships", "c"}, "c.id = u.membership_id").
		Join("INNER", []string{"emails", "e"}, "e.id = u.email_id").
		Limit(limit, offset)
	for k, v := range filter {
		if idx == 0 {
			sess.Where(k, v)
			idx++
		} else {
			sess = sess.Or(k, v)
		}
	}
	err = sess.Find(&users)

	return users, err
}

func (model *UserModel) GetUserIdByUsername(username string) int {
	user := new(Users)
	_, err := buildOrmCols(getExecutor(), "id").Where("username = ?", username).Get(user)
	if err != nil {
		return 0
	}
	return user.Id
}

// GetUserByUsername gets all information about a user
func (model *UserModel) GetUserByUsername(username string, cols ...string) *UserInfo {
	user := new(UserInfo)
	count, err := getExecutor().Table("users").Alias("u").
		Join("INNER", []string{"roles", "r"}, "r.id = u.role_id").
		Join("INNER", []string{"memberships", "c"}, "c.id = u.membership_id").
		Join("INNER", []string{"emails", "e"}, "e.id = u.email_id").
		Where("u.username = ?", username).Get(user, cols)
	if err != nil {
		zap.S().Error("get user error!! ", err.Error())
		return nil
	}
	if !count {
		return nil
	}
	return user
}

// GetUserForLogin gets user information specifically for login authentication
// This method ensures password and salt fields are properly retrieved
func (model *UserModel) GetUserForLogin(username string) *UserInfo {
	// Then get the complete user info with joins
	user := new(UserInfo)
	exist, err := getExecutor().Table("users").Alias("u").
		Join("INNER", []string{"roles", "r"}, "r.id = u.role_id").
		Join("INNER", []string{"memberships", "c"}, "c.id = u.membership_id").
		Join("INNER", []string{"emails", "e"}, "e.id = u.email_id").
		Where("u.username = ?", username).Get(user)
	if err != nil {
		zap.S().Error("get user for login error!! ", err.Error())
		return nil
	}
	if !exist {
		return nil
	}

	return user
}

// GetUserById gets user information by ID
func (model *UserModel) GetUserById(id int) (user *UserInfo, err error) {
	user = new(UserInfo)
	_, err = getExecutor().Table("users").Alias("u").
		Join("INNER", []string{"roles", "r"}, "r.id = u.role_id").
		Join("INNER", []string{"memberships", "c"}, "c.id = u.membership_id").
		Join("INNER", []string{"emails", "e"}, "e.id = u.email_id").
		Where("u.id = ?", id).
		Get(user)

	return user, err
}

func (model *UserModel) UpdateUserPassword(id int, newPassword, salt string) (int64, error) {
	res, err := getExecutor().ID(id).Cols("password", "salt").Update(Users{Password: newPassword, Salt: salt})
	if err != nil {
		zap.S().Error("update user error!! ", err.Error())
	}

	return res, err
}

func (model *UserModel) UpdateUserAvatar(id int, newAvatarUrl string) bool {
	res, err := getExecutor().ID(id).Cols("avatar_url").Update(Users{AvatarUrl: newAvatarUrl})
	if err != nil {
		zap.S().Error("update user avatar error!! ", err.Error())
		return false
	}

	return res > 0
}

// GetUserByEmailId gets a user by email ID
func (model *UserModel) GetUserByEmailId(emailId int) (*Users, error) {
	user := new(Users)
	has, err := getExecutor().Where("email_id = ?", emailId).Get(user)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return user, nil
}

// GetUserByEmail gets a user by email
func (model *UserModel) GetUserByEmail(email string) (*UserInfo, error) {
	user := new(UserInfo)

	// Then get the user with the matching email_id
	has, err := getExecutor().Table("users").Alias("u").
		Join("INNER", []string{"roles", "r"}, "r.id = u.role_id").
		Join("INNER", []string{"memberships", "c"}, "c.id = u.membership_id").
		Join("INNER", []string{"emails", "e"}, "e.id = u.email_id").
		Where("e.email = ?", email).
		Get(user)

	if err != nil {
		zap.S().Error("get user error!! ", err.Error())
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return user, nil
}

func (model *UserModel) CheckUserExist(user *Users, cols ...string) (bool, error) {
	sess := buildOrmCols(getExecutor(), cols...)
	return sess.Exist(user)
}
