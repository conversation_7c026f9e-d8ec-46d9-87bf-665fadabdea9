package models

import (
	"fmt"
	"next-meeting-backend/internal/pkg/config"

	_ "github.com/lib/pq"
	"go.uber.org/zap"
	"xorm.io/core"
	"xorm.io/xorm"
	"xorm.io/xorm/log"
)

var engine *xorm.Engine
var session *xorm.Session

type TransactionSession struct {
	session *xorm.Session
}

func getExecutor() *xorm.Session {
	if session != nil {
		return session
	}
	return engine.NewSession()
}

func buildOrmCols(sess *xorm.Session, cols ...string) *xorm.Session {
	if len(cols) > 0 && cols[0] == "all" {
		return sess.AllCols()
	}
	return sess.Cols(cols...)
}

func enableDebugMode(debug bool) {
	//
	if !debug {
		return
	}
	engine.ShowSQL(debug)
	engine.Logger().SetLevel(log.LOG_DEBUG)
}

func BuildPostgresEngine(conf config.PostgresConfig) {
	var err error
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		conf.Host, conf.Port, conf.User, conf.Password, conf.Database)
	engine, err = xorm.NewEngine("postgres", connStr)
	if err != nil {
		zap.S().Panicf("Failed to initialize database engine: %v", err)
	}
	if engine == nil {
		zap.S().Panic("Database engine is nil after initialization attempt without an explicit error.")
	}
	engine.SetMapper(core.GonicMapper{})
	enableDebugMode(conf.Debug)
}

func NewTransaction() TransactionSession {
	t := TransactionSession{session: engine.NewSession()}
	return t
}

func (s *TransactionSession) BeginTransaction() {
	if err := s.session.Begin(); err != nil {
		zap.S().Error("Failed to begin transaction", zap.Error(err))
	}
}

func (s *TransactionSession) CommitTransaction() {
	if err := s.session.Commit(); err != nil {
		zap.S().Error("Failed to commit transaction", zap.Error(err))
	}
}

func (s *TransactionSession) RollbackTransaction() {
	if err := s.session.Rollback(); err != nil {
		zap.S().Error("Failed to rollback transaction", zap.Error(err))
	}
}

func (s *TransactionSession) CloseSession() {
	if s.session.IsClosed() {
		return
	}
	_ = s.session.Close()
}
