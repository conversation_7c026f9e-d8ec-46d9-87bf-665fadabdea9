package models

import (
	"strings"
	"time"
)

type PaymentModel struct {
	Transactions
	Reviews
	Invoices
	TransactionInfo
}

type Transactions struct {
	Id              int       `json:"tid" xorm:"not null pk autoincr"`
	RequestUser     int       `json:"request_user"`
	TeamUsers       string    `json:"team_users"`
	MethodId        int       `json:"method_id"`
	Region          string    `json:"region"`
	TransactionId   string    `json:"transaction_id"`
	Amount          float32   `json:"amount"`
	Evidence        string    `json:"evidence"`
	Notes           string    `json:"notes"`
	TransactionTime time.Time `json:"transaction_time"`
	CreateTime      time.Time `json:"t_create_time"`
}

type Reviews struct {
	Id         int `json:"rid" xorm:"not null pk autoincr"`
	Tid        int
	Reviewer   int       `json:"reviewer"`
	Approved   bool      `json:"approved"`
	ReviewTime time.Time `json:"review_time"`
}

type Invoices struct {
	Id           int       `json:"iid" xorm:"not null pk autoincr"`
	Owner        int       `json:"owner"`
	Tid          int       `json:"tid"`
	TaxId        string    `json:"tax_id"`
	Organization string    `json:"organization"`
	Address      string    `json:"address"`
	ContactName  string    `json:"contact_name"`
	ContactEmail string    `json:"contact_email"`
	ContactPhone string    `json:"contact_phone"`
	Instruction  string    `json:"instruction"`
	Status       string    `json:"status"`
	CreateTime   time.Time `json:"i_create_time"`
}

type PaymentMethod struct {
	Id   int    `json:"method_id" xorm:"not null pk autoincr"`
	Name string `json:"method_name"`
}

func (pm *PaymentModel) AddTransaction(transaction Transactions, cols ...string) (int, error) {
	res, err := buildOrmCols(getExecutor(), cols...).Insert(&transaction)
	if err != nil {
		return 0, err
	}

	if res <= 0 {
		return 0, nil
	}

	return transaction.Id, nil
}

func (pm *PaymentModel) CheckPaymentStatusByUserId(userId int) ([]map[string]interface{}, error) {
	sql := "SELECT t.id as tid, approved FROM users u LEFT JOIN transactions t ON u.id = t.request_user LEFT JOIN reviews r ON t.id = r.tid WHERE t.request_user = ? ORDER BY tid desc LIMIT 1"
	data, err := getExecutor().QueryInterface(sql, userId)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (pm *PaymentModel) CheckPaymentStatusById(userId int) ([]map[string]interface{}, error) {
	sql := "SELECT t.id as tid, request_user, approved FROM users u LEFT JOIN transactions t ON u.id = t.request_user LEFT JOIN reviews r ON t.id = r.tid WHERE t.id = ? ORDER BY tid desc LIMIT 1"
	data, err := getExecutor().QueryInterface(sql, userId)
	if err != nil {
		return nil, err
	}

	return data, nil
}

type TransactionInfo struct {
	Tid             int       `json:"tid" xorm:"tid"`
	TransactionId   string    `json:"transaction_id" xorm:"transaction_id"`
	TeamUsers       string    `json:"team_users" xorm:"team_users"`
	Region          string    `json:"region" xorm:"region"`
	Amount          string    `json:"amount" xorm:"amount"`
	Evidence        string    `json:"evidence" xorm:"evidence"`
	Notes           string    `json:"notes" xorm:"notes"`
	CreateTime      time.Time `json:"t_create_time" xorm:"t_create_time"`
	MethodName      string    `json:"method_name" xorm:"method_name"`
	Reviewer        int       `json:"reviewer" xorm:"reviewer"`       // Consider *int or sql.NullInt64 if nullable
	Approved        bool      `json:"approved" xorm:"approved"`       // Consider sql.NullBool if nullable
	ReviewTime      time.Time `json:"review_time" xorm:"review_time"` // Consider *time.Time or sql.NullTime if nullable
	RequestUser     string    `json:"request_user" xorm:"request_user"`
	RequestUsername string    `json:"request_username" xorm:"request_username"`
	ReviewUser      string    `json:"review_user" xorm:"review_user"`         // Consider *string or sql.NullString if nullable
	ReviewUsername  string    `json:"review_username" xorm:"review_username"` // Consider *string or sql.NullString if nullable
	TransactionTime time.Time `json:"transaction_time" xorm:"transaction_time"`
}

func (pm *PaymentModel) GetTransactionByUserId(userId int) (*TransactionInfo, error) {
	transaction := new(TransactionInfo)
	selectFields := "t.id as tid, t.transaction_id, t.team_users, t.region, t.amount, t.evidence, t.notes, t.transaction_time, t.create_time as t_create_time, p.name as method_name, r.reviewer, r.approved, r.review_time, u.name as request_user, u.username as request_username, u2.name as review_user, u2.username as review_username"
	has, err := getExecutor().
		Table([]string{"transactions", "t"}).
		Select(selectFields).
		Join("INNER", []string{"payment_method", "p"}, "p.id = t.method_id").
		Join("LEFT", []string{"reviews", "r"}, "r.tid = t.id").
		Join("LEFT", []string{"users", "u"}, "u.id = t.request_user").
		Join("LEFT", []string{"users", "u2"}, "u2.id = r.reviewer").
		Where("t.request_user = ?", userId).Desc("t.create_time").Get(transaction)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return transaction, nil
}

func (pm *PaymentModel) GetTransactionById(tid int) (*Transactions, error) {
	tm := new(Transactions)
	_, err := getExecutor().Where("id = ?", tid).Get(tm)
	if err != nil {
		return nil, err
	}

	return tm, nil

}

// ListTransactions 列出支付记录
func (pm *PaymentModel) ListTransactions(limit, offset int, paramSql string, params ...interface{}) (res []map[string]interface{}, err error) {
	baseSql := "SELECT t.id as tid, t.team_users as team_users, t.region as region, t.transaction_id as transaction_id, t.amount as amount, t.evidence as evidence, t.transaction_time as transaction_time, t.create_time as t_create_time, p.name as method_name, r.reviewer as reviewer, r.approved as approved, r.review_time as review_time, u.name as request_user, u.username as request_username, u2.name as review_user, u2.username as review_username FROM transactions t INNER JOIN payment_method p ON p.id = t.method_id LEFT JOIN reviews r ON r.tid = t.id LEFT JOIN users u ON u.id = t.request_user LEFT JOIN users u2 ON u2.id = r.reviewer"
	orderSql := " ORDER BY t.create_time DESC"
	limitSql := " LIMIT ? OFFSET ?"
	sql := strings.Join([]string{baseSql, paramSql, orderSql, limitSql}, "")
	if paramSql == "" {
		// Call variadic QueryInterface with sql, limit, offset as individual args
		res, err = getExecutor().QueryInterface(sql, limit, offset)
	} else {
		// Build the full list of arguments to pass to QueryInterface.
		// The first argument must be the SQL string itself.
		finalArgs := []interface{}{sql}
		// Append the dynamic parameters received by ListTransactions (params).
		finalArgs = append(finalArgs, params...)
		// Append the limit and offset parameters.
		finalArgs = append(finalArgs, limit, offset)

		// Call QueryInterface by spreading all elements from finalArgs.
		// This effectively calls: QueryInterface(sql_string, dynamic_param1, ..., limit_val, offset_val)
		res, err = getExecutor().QueryInterface(finalArgs...)
	}

	if err != nil {
		return nil, err
	}

	return res, nil
}

func (pm *PaymentModel) UpdateTransaction(transaction Transactions, cols ...string) (int, error) {
	res, err := getExecutor().ID(transaction.Id).Cols(cols...).Update(transaction)
	if err != nil {
		return 0, err
	}

	if res <= 0 {
		return 0, nil
	}

	return transaction.Id, nil
}

func (pm *PaymentModel) AddReview(review *Reviews, cols ...string) error {
	res, err := buildOrmCols(getExecutor(), cols...).Insert(review)
	if err != nil {
		return err
	}

	if res <= 0 {
		return nil
	}

	return nil
}

func (pm *PaymentModel) AddInvoice(invoice *Invoices) (int, error) {
	res, err := getExecutor().Insert(invoice)
	if err != nil {
		return 0, err
	}

	if res <= 0 {
		return 0, nil
	}

	return invoice.Id, nil
}

func (pm *PaymentModel) UpdateInvoice(invoice *Invoices) (int, error) {
	res, err := getExecutor().Update(invoice)
	if err != nil {
		return 0, err
	}

	if res <= 0 {
		return 0, nil
	}

	return invoice.Id, nil
}

func (pm *PaymentModel) GetInvoiceByUserId(userId int) (*Invoices, error) {
	invoice := new(Invoices)
	has, err := getExecutor().Where("owner = ?", userId).Get(invoice)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return invoice, nil
}
