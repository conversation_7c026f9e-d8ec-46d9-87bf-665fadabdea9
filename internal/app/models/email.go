package models

import (
	"errors"
	"go.uber.org/zap"
	"next-meeting-backend/internal/app/models_field"
	"time"
)

type EmailModel struct {
}

type Emails struct {
	Id            int    `json:"eid" xorm:"not null pk autoincr"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified" xorm:"default false"`
	VerifiedTime  time.Time
}

// AddEmail adds a new email record and returns the ID
func (m *EmailModel) AddEmail(email *Emails, cols ...string) (int, error) {
	sess := buildOrmCols(getExecutor(), cols...)
	res, err := sess.Insert(email)
	if err != nil {
		return 0, err
	}

	if res <= 0 {
		return 0, errors.New("failed to insert email record")
	}

	return email.Id, nil
}

func (m *EmailModel) GetEmail(email *Emails, cols ...string) (res *Emails, err error) {
	sess := buildOrmCols(getExecutor(), cols...)
	has, err := sess.Get(email)
	if err != nil {
		zap.S().Error(err.Error())
		return nil, err
	}
	if !has {
		return nil, nil
	}

	return email, nil
}

// GetEmailByAddress gets an email record by email address
func (m *EmailModel) GetEmailByAddress(emailAddress string) (*Emails, error) {
	email := new(Emails)
	has, err := getExecutor().Where("email = ?", emailAddress).Get(email)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return email, nil
}

func (m *EmailModel) CheckEmailExist(emails *Emails, cols ...string) (bool, error) {
	sess := buildOrmCols(getExecutor(), cols...)
	return sess.Exist(emails)
}

func (m *EmailModel) UpdateEmail(email *Emails, cols ...string) (int, error) {
	sess := buildOrmCols(getExecutor(), cols...)
	res, err := sess.ID(email.Id).Update(email)
	if err != nil {
		zap.S().Error(err.Error())
		return 0, err
	}

	if res <= 0 {
		return 0, errors.New("failed to update email record")
	}

	return email.Id, nil
}

// VerifyEmail updates the email verification status
func (m *EmailModel) VerifyEmail(emailId int) bool {
	email := &Emails{
		Id:            emailId,
		EmailVerified: true,
		VerifiedTime:  time.Now(),
	}

	res, err := m.UpdateEmail(email, models_field.EmailVerified, models_field.EmailVerifiedTime)
	if err != nil {
		return false
	}

	return res > 0
}
