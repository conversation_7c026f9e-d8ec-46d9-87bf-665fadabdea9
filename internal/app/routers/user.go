package routers

import (
	"next-meeting-backend/internal/app/handler"
	"next-meeting-backend/internal/middleware/casbin"
	"next-meeting-backend/internal/middleware/jwt"

	"github.com/gin-gonic/gin"
)

func UserRouteRegister(e *gin.Engine) {
	userHandler := handler.UserHandler{}
	// User routes that don't require authentication
	publicUserGroup := e.Group("/api/users")
	{
		// Get all users - requires "user" object and "read" action permission
		publicUserGroup.GET("/check", userHandler.CheckExistUsername)
	}
	// User routes that require authentication
	// Update user information - requires "user" object and "update" action permission
	protectedUserGroup := e.Group("/api/users")
	protectedUserGroup.Use(jwt.JWT())
	{
		protectedUserGroup.PUT("/:id", casbin.Authorize("user", "update"), userHandler.UpdateUserHandler)
		// Update user password - requires "user" object and "update" action permission
		protectedUserGroup.PATCH("/:id/password", casbin.Authorize("user", "update"), userHandler.UpdatePasswordHandler)
		// Get specific user information
		protectedUserGroup.GET("/:id", casbin.Authorize("user", "read"), userHandler.GetUserHandler)
		// Update specific user information
		protectedUserGroup.PATCH("/:id", casbin.Authorize("user", "update"), userHandler.UpdateUserHandler)
		protectedUserGroup.PATCH("/:id/email", casbin.Authorize("user", "update"), userHandler.UpdateUserEmailHandler)
		protectedUserGroup.GET("/check/payment/:username", userHandler.CheckExistUsername)
	}

}
