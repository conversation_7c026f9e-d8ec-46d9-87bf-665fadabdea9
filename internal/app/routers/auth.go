package routers

import (
	"github.com/gin-gonic/gin"
	"next-meeting-backend/internal/app/handler"
	"next-meeting-backend/internal/middleware/jwt"
	"next-meeting-backend/internal/middleware/turnstile"
)

// AuthRouteRegister registers all authentication related routes
func AuthRouteRegister(e *gin.Engine) {
	authHandler := &handler.UserHandler{}
	// Public routes that don't require authentication
	authForm := e.Group("/api/auth")
	authForm.Use(turnstile.Turnstile())
	{
		authForm.POST("/login", authHandler.LoginHandler)
		authForm.POST("/register", authHandler.RegisterHandler)
		authForm.POST("/forgot-password", authHandler.ForgotPasswordHandler)
		authForm.POST("/reset-password", authHandler.ResetPasswordHandler)
	}

	public := e.Group("/api/")
	{
		public.POST("/verify/email", authHandler.VerifyEmailHandler)
	}

	verifyForm := e.Group("/api/verify")
	verifyForm.Use(jwt.JWT())
	{
		verifyForm.POST("/resend", authHandler.ResendVerificationHandler)
	}
}
