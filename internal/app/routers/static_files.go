package routers

import (
	"github.com/gin-gonic/gin"
	"log"
	"path/filepath"
)

func StaticFilesRouteRegister(e *gin.Engine) {
	e.<PERSON>("/config", logPath("./config"))
	e.<PERSON>("/templates", logPath("./internal/templates"))
}

func logPath(path ...string) string {
	p := filepath.Join(path...)
	abs, err := filepath.Abs(p)
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("path: %s", abs)

	return abs
}
