package routers

import (
	"github.com/gin-gonic/gin"
	"next-meeting-backend/internal/app/handler"
	"next-meeting-backend/internal/middleware/casbin"
	"next-meeting-backend/internal/middleware/jwt"
)

func AdminRouteRegister(e *gin.Engine) {
	var payment handler.AdminPaymentHandler
	var user handler.AdminUserHandler

	admin := e.Group("/api/admin")
	admin.Use(jwt.JWT())
	admin.Use(casbin.AuthorizeByRole("admin"))
	{
		admin.GET("/users", user.AdminGetUsersHandler)
		admin.PUT("/users/:id", user.AdminUpdateUserHandler)
		admin.DELETE("/users/:id", user.AdminDeleteUserHandler)
		admin.POST("/users", user.AdminAddUserHandler)

		admin.GET("/payments", payment.ListPayments)
		admin.POST("/payment/review/:id", payment.AddReview)
	}
}
