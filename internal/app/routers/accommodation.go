package routers

import (
	"next-meeting-backend/internal/app/handler"
	"next-meeting-backend/internal/middleware/jwt"

	"github.com/gin-gonic/gin"
)

func AccommodationRouteRegister(e *gin.Engine) {
	accommodation := &handler.AccommodationHandler{}
	accommodationGroup := e.Group("/api/accommodations")
	accommodationGroup.Use(jwt.JWT())
	{
		accommodationGroup.POST("/submit", accommodation.ReverseRoom)
		accommodationGroup.GET("/get", accommodation.GetHotels)
		accommodationGroup.GET("/get/:user_id", accommodation.GetMyAccommodations)
		//accommodationGroup.DELETE("/cancel/:user_id", accommodation.CancelRoomReservation)
		accommodationGroup.GET("/:username/status", accommodation.CheckUserReservation)
	}

}
