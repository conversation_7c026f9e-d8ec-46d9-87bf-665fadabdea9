package routers

import (
	"next-meeting-backend/internal/middleware/cors"

	"github.com/gin-gonic/gin"
)

func SetupRouter() *gin.Engine {
	router := gin.Default()
	router.Use(cors.Cors())
	// 静态目录
	StaticFilesRouteRegister(router)
	// 管理员
	AdminRouteRegister(router)

	// Register all route groups
	UserRouteRegister(router)
	// 认证路由
	AuthRouteRegister(router)
	PolicyRouteRegister(router)

	// 支付信息路由
	PaymentRoutesRegister(router)

	// 摘要提交
	AbstractRouteRegister(router)

	// 住宿信息
	AccommodationRouteRegister(router)
	return router
}
