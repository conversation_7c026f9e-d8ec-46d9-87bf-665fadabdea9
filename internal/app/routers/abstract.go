package routers

import (
	"github.com/gin-gonic/gin"
	"next-meeting-backend/internal/app/handler"
	"next-meeting-backend/internal/middleware/jwt"
)

func AbstractRouteRegister(e *gin.Engine) {
	abstractHandler := &handler.AbstractHandler{}
	abstractGroup := e.Group("/api/abstracts")
	abstractGroup.Use(jwt.JWT())
	{
		abstractGroup.POST("/submit/:user_id", abstractHandler.SubmitAbstract)
		abstractGroup.GET("/get/:user_id", abstractHandler.GetAbstract)
		abstractGroup.PUT("update/:id", abstractHandler.UpdateAbstractHandler)
	}
}
