package routers

import (
	"next-meeting-backend/internal/app/handler"
	"next-meeting-backend/internal/middleware/casbin"
	"next-meeting-backend/internal/middleware/jwt"

	"github.com/gin-gonic/gin"
)

// PolicyRouteRegister registers all policy management routes
func PolicyRouteRegister(e *gin.Engine) {
	// Policy management routes - admin only
	policyGroup := e.Group("/api/policies")
	policyGroup.Use(jwt.JWT())
	policyGroup.Use(casbin.AuthorizeByRole("admin"))
	{
		// Initialize basic policies
		policyGroup.POST("/initialize", handler.InitializeBasicPoliciesHandler)
		// Add a new policy rule
		policyGroup.POST("/", handler.AddPolicyHandler)
		// Remove a policy rule
		policyGroup.DELETE("/", handler.RemovePolicyHandler)
		// Assign a role to a user
		policyGroup.POST("/roles", handler.AssignRoleHandler)
		// Remove a role from a user
		policyGroup.DELETE("/roles", handler.RemoveRoleHandler)
		// Get all roles for a user
		policyGroup.GET("/roles/:id", handler.GetUserRolesHandler)
	}
}
