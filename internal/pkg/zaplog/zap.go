package zaplog

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"next-meeting-backend/internal/pkg/config"
)

var conf zap.Config

func InitDevEncoderConfig() *zap.Logger {
	conf = zap.NewDevelopmentConfig()
	conf.OutputPaths = []string{"stderr"}
	conf.ErrorOutputPaths = []string{"stderr"}
	conf.Level = zap.NewAtomicLevelAt(zap.DebugLevel)
	conf.Encoding = "console"
	conf.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	conf.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	return buildZap()
}

func InitProdEncoderConfig(zapConfig config.ZapConfig) *zap.Logger {
	conf = zap.NewProductionConfig()
	conf.Level = zap.NewAtomicLevelAt(zap.DebugLevel)
	conf.Encoding = "json"
	conf.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	conf.OutputPaths = []string{zapConfig.InfoLogPath}
	conf.ErrorOutputPaths = []string{zapConfig.ErrorLogPath}

	return buildZap()
}

func buildZap() *zap.Logger {
	logger, err := conf.Build()
	if err != nil {
		panic(err)
	}
	zap.ReplaceGlobals(logger)

	return logger
}

func RevokeZap(logger *zap.Logger) {
	if !conf.Development {
		err := logger.Sync()
		if err != nil {
			panic(err)
		}
	}
}
