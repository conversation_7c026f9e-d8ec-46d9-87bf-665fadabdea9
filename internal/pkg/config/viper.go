package config

import (
	"fmt"
	"github.com/spf13/viper"
	"log"
	"os"
)

type AppConfig struct {
	RunMode string `mapstructure:"run_mode"`
	Port    int    `mapstructure:"port"`
	WebUrl  string `mapstructure:"web_url"`

	TurnstileConfig TurnstileConfig `mapstructure:"turnstile"`
	DbConfig        DatabaseConfig  `mapstructure:"database"`
	ZapConfig       ZapConfig       `mapstructure:"logs"`
	EmailConfig     EmailConfig     `mapstructure:"email"`
	JwtConfig       JwtConfig       `mapstructure:"jwt"`
	S3Config        S3Config        `mapstructure:"s3"`
}

var App *AppConfig

type TurnstileConfig struct {
	Secret string `mapstructure:"secret"`
}
type DatabaseConfig struct {
	Psql  PostgresConfig `mapstructure:"postgres"`
	Redis RedisConfig    `mapstructure:"redis"`
}

type PostgresConfig struct {
	Debug    bool   `mapstructure:"debug"`
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"db_name"`
}

type ZapConfig struct {
	InfoLogPath  string `mapstructure:"debug_log_path"`
	ErrorLogPath string `mapstructure:"err_log_path"`
}

type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"db_name"`
}

type EmailConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	From         string `mapstructure:"from"`
	TemplatePath string `mapstructure:"template_path"`
}

type JwtConfig struct {
	TokenExpire int `mapstructure:"token_expire"`
}

type S3Config struct {
	Endpoint string `mapstructure:"endpoint"`
	Region   string `mapstructure:"Region"`
	Secure   bool   `mapstructure:"secure"`
	Bucket   string `mapstructure:"bucket"`

	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
}

func init() {
	App = readConfig()
}

func readConfig() (app *AppConfig) {
	rootPath := os.Getenv("ROOT_PATH")
	viper.SetConfigName("app")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(fmt.Sprintf("%s/config/", rootPath))

	err := viper.ReadInConfig()
	if err != nil {
		log.Fatalf("Error reading config file: %s", err)
	}

	Unmarshal(&app)
	return app
}

func Unmarshal(config interface{}) {
	err := viper.Unmarshal(&config)
	if err != nil {
		log.Fatalf("Error unmarshalling config: %s", err)
	}
}
