package jwt

import (
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"github.com/spf13/viper"
	"next-meeting-backend/internal/pkg/config"
	"time"
)

var stSignKey = []byte(viper.GetString("jwt.SignKey"))

// CustomClaims 注册声明是JWT声明集的结构化版本，仅限于注册声明名称
type CustomClaims struct {
	Id            int
	Username      string
	Password      string
	EmailVerified bool
	jwt.RegisteredClaims
}

func (j CustomClaims) GetExpirationTime() (*jwt.NumericDate, error) {
	return j.ExpiresAt, nil
}

func (j CustomClaims) GetIssuedAt() (*jwt.NumericDate, error) {
	return j.IssuedAt, nil
}

func (j CustomClaims) GetNotBefore() (*jwt.NumericDate, error) {
	return j.NotBefore, nil
}

func (j CustomClaims) GetIssuer() (string, error) {
	return j.Issuer, nil
}

func (j CustomClaims) GetSubject() (string, error) {
	return j.Subject, nil
}

func (j CustomClaims) GetAudience() (jwt.ClaimStrings, error) {
	return j.Audience, nil
}

// GenerateToken 生成Token
func GenerateToken(id int, name string) (string, error) {
	t := time.Duration(config.App.JwtConfig.TokenExpire)

	// 初始化
	iJwtCustomClaims := CustomClaims{
		Id:       id,
		Username: name,
		RegisteredClaims: jwt.RegisteredClaims{
			// 设置过期时间 在当前基础上 添加一个小时后 过期
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(t * time.Hour)),
			// 颁发时间 也就是生成时间
			IssuedAt: jwt.NewNumericDate(time.Now()),
			//主题
			Subject: "Token",
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, iJwtCustomClaims)
	return token.SignedString(stSignKey)
}

// GenerateRandomToken generates a random token with the specified length
func GenerateRandomToken(length int) string {
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		// If random generation fails, use a timestamp-based fallback
		return base64.URLEncoding.EncodeToString([]byte(fmt.Sprintf("%d", time.Now().UnixNano())))
	}
	return base64.URLEncoding.EncodeToString(b)
}

// ParseToken 解析token
func ParseToken(tokenStr string) (CustomClaims, error) {
	iJwtCustomClaims := CustomClaims{}
	//ParseWithClaims是NewParser().ParseWithClaims()的快捷方式
	token, err := jwt.ParseWithClaims(tokenStr, &iJwtCustomClaims, func(token *jwt.Token) (interface{}, error) {
		return stSignKey, nil
	})

	if err == nil && !token.Valid {
		err = errors.New("invalid Token")
	}
	return iJwtCustomClaims, err
}

func IsTokenValid(tokenStr string) bool {
	_, err := ParseToken(tokenStr)
	fmt.Println(err)
	if err != nil {
		return false
	}
	return true
}
