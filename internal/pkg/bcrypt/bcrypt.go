package bcrypt

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

// GenerateSalt generates a random salt for password hashing
func GenerateSalt() (string, error) {
	// Generate a random 16-byte salt
	bytes := make([]byte, 16)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}

	// Convert to hex string for storage
	return hex.EncodeToString(bytes), nil
}

// HashPassword hashes a password with the provided salt
func HashPassword(password string, salt string) (string, error) {
	if password == "" {
		zap.S().Error("[HashPassword] Error: Password cannot be empty")
		return "", errors.New("password cannot be empty")
	}

	if salt == "" {
		zap.S().Error("[HashPassword] Error: Salt cannot be empty")
		return "", errors.New("salt cannot be empty")
	}

	// Combine password and salt
	originalSaltedPassword := password + salt
	saltedPassword := originalSaltedPassword
	if len(saltedPassword) > 72 {
		saltedPassword = saltedPassword[:72]
		zap.S().Debugf("[HashPassword] SaltedPassword truncated to 72 chars. New Length: %d, Value (first 10 chars): %.10s...", len(saltedPassword), saltedPassword)
	}

	// Hash the salted password using bcrypt
	hash, err := bcrypt.GenerateFromPassword([]byte(saltedPassword), bcrypt.DefaultCost)
	if err != nil {
		zap.S().Errorf("[HashPassword] Error generating hash: %v", err)
		return "", err
	}
	return string(hash), nil
}

// CheckPassword checks if a password matches the hashed password
func CheckPassword(password string, salt string, hashedPassword string) bool {
	if password == "" || salt == "" || hashedPassword == "" {
		zap.S().Error("[CheckPassword] Error: One or more inputs are empty")
		return false
	}

	// Combine password and salt
	originalSaltedPassword := password + salt
	saltedPassword := originalSaltedPassword

	if len(saltedPassword) > 72 {
		saltedPassword = saltedPassword[:72]
		zap.S().Debugf("[CheckPassword] SaltedPassword truncated to 72 chars. New Length: %d, Value (first 10 chars): %.10s...", len(saltedPassword), saltedPassword)
	}

	// Compare the salted password with the hash
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(saltedPassword))
	return err == nil
}
