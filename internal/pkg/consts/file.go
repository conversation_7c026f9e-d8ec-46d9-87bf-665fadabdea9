package consts

type CacheDir string
type RemoteDir string
type FileSizeLimit int64

const (
	CacheRootDir       CacheDir      = "/cache/"
	CacheDocumentsDir  CacheDir      = "/cache/documents/"
	CacheAvatarsDir    CacheDir      = "/cache/avatars/"
	CacheReceiptsDir   CacheDir      = "/cache/receipts/"
	RemoteAvatarPath   RemoteDir     = "/avatars/"
	RemoteAbstractPath RemoteDir     = "/abstracts/"
	RemoteDocumentPath RemoteDir     = "/documents/"
	RemoteReceiptPath  RemoteDir     = "/receipts/"
	ReceiptMaxSize     FileSizeLimit = 10 * 1024 * 1024
	AbstractMaxSize    FileSizeLimit = 10 * 1024 * 1024
	ImageQuality                     = 85
	PdfCompression                   = 2
)
