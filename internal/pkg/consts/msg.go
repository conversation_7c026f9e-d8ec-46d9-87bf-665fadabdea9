package consts

var MsgFlags = map[int]string{
	Success:                          "ok",
	Error:                            "fail",
	InvalidParams:                    "Invalid request parameters",
	ErrorAuthCheckTokenFail:          "Token authentication failed",
	ErrorAuthCheckTokenTimeout:       "Token has expired",
	ErrorInvalidCredentials:          "Invalid username or password",
	ErrorUserNotFound:                "User does not exist",
	ErrorPasswordIncorrect:           "Incorrect password",
	ErrorUserAlreadyExists:           "User already exists",
	ErrorAuthCheckPermissionDenied:   "Insufficient permissions",
	ErrorUploadFileSizeLimitExceeded: "File size limit exceeded",
	ErrorUploadFileTypeNotAllowed:    "File type not allowed",
}

// GetMsg get error information based on Code
func GetMsg(code int) string {
	msg, ok := MsgFlags[code]
	if ok {
		return msg
	}

	return MsgFlags[Error]
}
