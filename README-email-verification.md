# Email Verification Documentation

This document describes the implementation of email verification functionality in the Next Meeting Backend application.

## Overview

The email verification feature ensures that the email address provided by users is valid and belongs to them. This feature includes:

- Sending verification emails during registration
- API endpoints for email verification
- Functionality to resend verification emails
- Email verification status check during login

## Database Changes

Two new fields have been added to the `users` table:

```sql
"email_verified" BOOLEAN NOT NULL DEFAULT false,
"verification_token" VARCHAR(128)
```

- `email_verified`: Indicates whether the user's email has been verified
- `verification_token`: Stores the unique token used for email verification

## API Endpoints

### 1. Email Verification

**URL**: `/api/auth/verify-email`  
**Method**: `POST`  
**Description**: Verifies the user's email  
**Request Body**:
```json
{
  "token": "verification token"
}
```
**Response**:
- Success (200 OK):
```json
{
  "code": 200,
  "msg": "Email verification successful",
  "data": null
}
```
- Failure (400 Bad Request):
```json
{
  "code": 400,
  "msg": "Invalid or expired verification token",
  "data": null
}
```

### 2. Resend Verification Email

**URL**: `/api/auth/resend-verification`  
**Method**: `POST`  
**Description**: Resends the verification email  
**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```
**Response**:
- Success (200 OK):
```json
{
  "code": 200,
  "msg": "Verification email has been resent",
  "data": null
}
```
- Failure (400 Bad Request):
```json
{
  "code": 400,
  "msg": "User does not exist or email is already verified",
  "data": null
}
```

## Email Templates

The verification email uses the `verify_email.html` template located in the `internal/templates/email/` directory. This template includes:

- A verification link button
- A plain text verification link (for email clients that don't support HTML)
- Verification instructions

## Registration Flow

1. User submits the registration form
2. System creates a user account, sets `email_verified = false` and generates a unique `verification_token`
3. System sends an email with the verification link to the user's email address
4. User clicks the verification link in the email
5. System validates the token, sets `email_verified` to `true`, and clears the `verification_token`
6. System sends a welcome email

## Login Restrictions

We provide two login mechanisms:

1. **Full Access**: Users with verified emails can access all system features.

2. **Limited Access**: Users with unverified emails can log in to the system but can only access limited functionality, such as viewing their profile and resending verification emails.

### Limited Access Mechanism

Users with unverified emails can access limited functionality through the following API endpoints:

- `/api/limited/profile`: View personal profile
- Other limited functionality...

These endpoints don't require email verification, allowing users to use basic features before verifying their email.

## Solutions for Email Reception Issues

If users don't receive verification emails, they can take the following steps:

1. **Resend Verification Email**:
   - Users can request to resend the verification email using the `/api/auth/resend-verification` endpoint
   - The frontend should provide a prominent "Resend Verification Email" button

2. **Check Spam Folder**:
   - Remind users to check their spam folder, as verification emails might be misclassified

3. **Use Limited Access**:
   - Even with an unverified email, users can still use basic system features
   - Users can log in and access limited functionality through the limited access mechanism

4. **Contact Administrator**:
   - If the above methods don't work, users can contact the system administrator to manually verify their email

### Frontend Implementation Example

Add prompts and help information on the login page:

```html
<div class="email-verification-help">
  <h3>Can't receive verification email?</h3>
  <ul>
    <li><button onclick="resendVerification()">Resend Verification Email</button></li>
    <li>Please check your spam folder</li>
    <li>You can still <a href="/login?limited=true">log in with limited functionality</a></li>
    <li>If the problem persists, please <a href="/contact">contact the administrator</a></li>
  </ul>
</div>
```

## Integration and Dependencies

The email verification feature integrates with the existing Casbin permission system and depends on:

- Email service (using gopkg.in/gomail.v2)
- JWT token generation (for creating verification tokens)

## Security Considerations

- Verification tokens are created using a cryptographically secure random generator
- Verification links are valid for 24 hours
- Verification tokens are cleared after successful verification to prevent reuse

## Usage Examples

### Frontend Verification Flow Example

1. After user registration, display a prompt:

```
Registration successful! Please check your email and click the verification link to complete registration.
```

2. If users don't receive the verification email, they can use the resend function:

```javascript
async function resendVerification(email) {
  const response = await fetch('/api/auth/resend-verification', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email }),
  });
  const result = await response.json();
  return result;
}
```

3. Handling the verification link:

```javascript
async function verifyEmail(token) {
  const response = await fetch('/api/auth/verify-email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ token }),
  });
  const result = await response.json();
  return result;
}
